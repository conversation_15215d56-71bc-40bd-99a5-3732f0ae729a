import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

const _kLocaleStorageKey = '__locale_key__';

class FFLocalizations {
  FFLocalizations(this.locale);

  final Locale locale;

  static FFLocalizations of(BuildContext context) =>
      Localizations.of<FFLocalizations>(context, FFLocalizations)!;

  static List<String> languages() => ['ko', 'en'];

  static late SharedPreferences _prefs;
  static Future initialize() async =>
      _prefs = await SharedPreferences.getInstance();
  static Future storeLocale(String locale) =>
      _prefs.setString(_kLocaleStorageKey, locale);
  static Locale? getStoredLocale() {
    final locale = _prefs.getString(_kLocaleStorageKey);
    return locale != null && locale.isNotEmpty ? createLocale(locale) : null;
  }

  String get languageCode => locale.toString();
  String? get languageShortCode =>
      _languagesWithShortCode.contains(locale.toString())
          ? '${locale.toString()}_short'
          : null;
  int get languageIndex => languages().contains(languageCode)
      ? languages().indexOf(languageCode)
      : 0;

  String getText(String key) =>
      (kTranslationsMap[key] ?? {})[locale.toString()] ?? '';

  String getVariableText({
    String? koText = '',
    String? enText = '',
  }) =>
      [koText, enText][languageIndex] ?? '';

  static const Set<String> _languagesWithShortCode = {
    'ar',
    'az',
    'ca',
    'cs',
    'da',
    'de',
    'dv',
    'en',
    'es',
    'et',
    'fi',
    'fr',
    'gr',
    'he',
    'hi',
    'hu',
    'it',
    'km',
    'ku',
    'mn',
    'ms',
    'no',
    'pt',
    'ro',
    'ru',
    'rw',
    'sv',
    'th',
    'uk',
    'vi',
  };
}

class FFLocalizationsDelegate extends LocalizationsDelegate<FFLocalizations> {
  const FFLocalizationsDelegate();

  @override
  bool isSupported(Locale locale) {
    final language = locale.toString();
    return FFLocalizations.languages().contains(
      language.endsWith('_')
          ? language.substring(0, language.length - 1)
          : language,
    );
  }

  @override
  Future<FFLocalizations> load(Locale locale) =>
      SynchronousFuture<FFLocalizations>(FFLocalizations(locale));

  @override
  bool shouldReload(FFLocalizationsDelegate old) => false;
}

Locale createLocale(String language) => language.contains('_')
    ? Locale.fromSubtags(
        languageCode: language.split('_').first,
        scriptCode: language.split('_').last,
      )
    : Locale(language);

final kTranslationsMap = <Map<String, Map<String, String>>>[
  // RecipeDetailPage
  {
    '0apm2j8u': {
      'ko': '설명',
      'en': 'Description',
    },
    'mi0n6wv0': {
      'ko': '재료',
      'en': 'Ingredients',
    },
    'l8cux9w0': {
      'ko': '저장',
      'en': 'Save',
    },
    't490csbw': {
      'ko': '수정',
      'en': 'Modify',
    },
    'kjkxa5xc': {
      'ko': '삭제',
      'en': 'Delete',
    },
    'y3sqdutv': {
      'ko': '레시피 삭제',
      'en': 'Delete Recipe',
    },
    'zona0782': {
      'ko': '네',
      'en': 'Ok',
    },
    '4pwbpx67': {
      'ko': '아니요',
      'en': 'No',
    },
    '6l0beo6z': {
      'ko': 'Home',
      'en': 'Home',
    },
  },
  // RecipeListPage
  {
    'hc95rt32': {
      'ko': '레시피 목록',
      'en': 'Recipe List',
    },
    'dg8ak59w': {
      'ko': '레시피가 없습니다.\n새로운 레시피를 만들어보세요.',
      'en': 'No recipes found. Try creating a new recipe.',
    },
    'vt9a65lj': {
      'ko': 'Home',
      'en': 'Home',
    },
    // showcase
    '1v3z1z9': {
      'ko': '레시피 내용 확인하기',
      'en': 'View Recipe Details',
    },
    '1d3z1z5': {
      'ko': '상세한 레시피를 확인하려면 리스트의 레시피를 클릭하세요.',
      'en': 'Click on a recipe in the list to view the detailed recipe.',
    },
  },
  // MainPage
  {
    '854x0c4z': {
      'ko': '버려지는 재료가 없게,\n냉털',
      'en': 'No wasted ingredients,\nLeftOvers',
    },
    '3g9v3pop': {
      'ko': '레시피 찾기',
      'en': 'Find Recipes',
    },
    'h7sdoxgz': {
      'ko': '재료',
      'en': 'Ingredients',
    },
    'bk9fkhho': {
      'ko': '재료 검색',
      'en': 'Search Ingredients',
    },
    'd3l2u7xl': {
      'ko': 'Option 1',
      'en': 'Option 1',
    },
    'klz6536r': {
      'ko': '입력 가능 재료 개수 초과',
      'en': 'Exceeded the allowable number of input materials',
    },
    'kvdx91bj': {
      'ko': '입력 가능한 재료의 총 갯수는 5개 이하입니다.',
      'en': 'The total number of input materials allowed is 5 or fewer.',
    },
    'c4k8i2wg': {
      'ko': '10-20 분',
      'en': '10-20 mins',
    },
    '1nmgjrot': {
      'ko': '10-20 분',
      'en': '10-20 mins',
    },
    'd69mi3j5': {
      'ko': '20-30 분',
      'en': '20-30 mins',
    },
    'nik1ib1x': {
      'ko': '30-45 분',
      'en': '30-45 mins',
    },
    'ok2zhnac': {
      'ko': '45분 이상',
      'en': '45+ mins',
    },
    'd66z1f48': {
      'ko': '검색',
      'en': 'Search',
    },
    'xxa93fwc': {
      'ko': '오류메세지',
      'en': 'Error Message',
    },
    'ou4fq465': {
      'ko': '일시적인 네트워크 오류가 발생했습니다. 재시도 해주세요.',
      'en': 'A temporary network error has occurred. Please try again.',
    },
    'dxf0jtxw': {
      'ko': '확인',
      'en': 'Ok',
    },
    'am8cwv3l': {
      'ko': 'Home',
      'en': 'Home',
    },
    // showcase
    '1v3z1z5': {
      'ko': '레시피를 찾기 위해 원하는 재료를 입력해주세요.',
      'en': 'Enter the ingredients you want to find recipes for.',
    },
    '1v3z1z6': {
      'ko': '원하는 조리 시간대를 선택해주세요.',
      'en': 'Select the desired cooking time.',
    },
    '1v3z1z7': {
      'ko': '원하는 난이도를 선택해주세요.',
      'en': 'Select the desired difficulty.',
    },
    '1v3z1z8': {
      'ko': '검색 버튼을 눌러 레시피를 찾아보세요.',
      'en': 'Press the search button to find recipes.',
    },
  },
  // WriteNewRecipePage
  {
    '3fg1hsoq': {
      'ko': '저장',
      'en': 'Save',
    },
    '7soghb8s': {
      'ko': '필수입력 항목 누락',
      'en': 'Required Input Missing',
    },
    '2uorxr56': {
      'ko': '레시피의 제목, 재료, 단계는 필수적으로 기입해주세요.',
      'en':
          'The recipe title, ingredients, and steps are mandatory fields. Please fill them out.',
    },
    'h9pebz97': {
      'ko': '확인',
      'en': 'Ok',
    },
    'c8449hsi': {
      'ko': '새로운 레시피 작성',
      'en': 'Write a new recipe',
    },
    'mfzm138c': {
      'ko': 'Home',
      'en': 'Home',
    },
  },
  // ModifyRecipePage
  {
    'a5yyztc8': {
      'ko': '수정',
      'en': 'Modify',
    },
    'j4yttsc8': {
      'ko': '필수입력 항목 누락',
      'en': 'Required Input Missing',
    },
    'pf4mxj5n': {
      'ko': '레시피의 제목, 재료, 단계는 필수적으로 기입해주세요.',
      'en':
          'The recipe title, ingredients, and steps are mandatory fields. Please fill them out.',
    },
    'ukgko6lj': {
      'ko': '확인',
      'en': 'Ok',
    },
    'xbgmorzj': {
      'ko': '레시피 수정',
      'en': 'Modify Recipe',
    },
    'ruo2w7i4': {
      'ko': 'Home',
      'en': 'Home',
    },
  },
  // RecipeForm
  {
    'cq3n4dq0': {
      'ko': '제목',
      'en': 'Title',
    },
    'dvm4tdi9': {
      'ko': '설명',
      'en': 'Description',
    },
    '3cvr02vc': {
      'ko': '재료',
      'en': 'Ingredients',
    },
    'zqlh4e24': {
      'ko': '재료 검색',
      'en': 'Search Ingredients',
    },
    'qo96vrps': {
      'ko': 'Option 1',
      'en': 'Option 1',
    },
    'uaea8w45': {
      'ko': '10-20 분',
      'en': '10-20 mins',
    },
    '6nraua3d': {
      'ko': '20-30 분',
      'en': '20-30 mins',
    },
    'jfee29u6': {
      'ko': '30-40 분',
      'en': '30-40 mins',
    },
    'cptqjls6': {
      'ko': '45분 이상',
      'en': '45+ mins',
    },
    '7uoz9tnm': {
      'ko': '조리 단계',
      'en': 'Cooking Steps',
    },
    '13ks0qdf': {
      'ko': '단계를 입력하세요.',
      'en': 'Enter the steps.',
    },
  },
  // LanguagesOptionsComponent
  {
    '2a5ucwxh': {
      'ko': '언어 옵션',
      'en': 'Language Options',
    },
    'pga8sbl8': {
      'ko': '🇰🇷 한국어',
      'en': '🇰🇷 한국어',
    },
    'svkzpxc0': {
      'ko': '🇬🇧 English',
      'en': '🇬🇧 English',
    },
  },
  // Miscellaneous
  {
    'f16o4kqn': {
      'ko': '',
      'en': '',
    },
    'w3xs759k': {
      'ko': '',
      'en': '',
    },
    '0bf59hh1': {
      'ko': '',
      'en': '',
    },
    '9nx2k20l': {
      'ko': '',
      'en': '',
    },
    'hkos9q8e': {
      'ko': '',
      'en': '',
    },
    'uv151qtp': {
      'ko': '',
      'en': '',
    },
    'ouhahlvs': {
      'ko': '',
      'en': '',
    },
    '6gww49px': {
      'ko': '',
      'en': '',
    },
    '26ysrugo': {
      'ko': '',
      'en': '',
    },
    'mux9ow7n': {
      'ko': '',
      'en': '',
    },
    'caeher28': {
      'ko': '',
      'en': '',
    },
    '36zm1m6v': {
      'ko': '',
      'en': '',
    },
    '69qs4eyc': {
      'ko': '',
      'en': '',
    },
    'lkxuy46w': {
      'ko': '',
      'en': '',
    },
    'b2cherts': {
      'ko': '',
      'en': '',
    },
    'qbxs3yms': {
      'ko': '',
      'en': '',
    },
    '06d0cuu0': {
      'ko': '',
      'en': '',
    },
    'p1y6tj42': {
      'ko': '',
      'en': '',
    },
    'toq8e50f': {
      'ko': '',
      'en': '',
    },
    'ifrpnz3s': {
      'ko': '',
      'en': '',
    },
    'm7ztr3w2': {
      'ko': '',
      'en': '',
    },
    '3cu90hm9': {
      'ko': '',
      'en': '',
    },
    '3rdynycc': {
      'ko': '',
      'en': '',
    },
    'euqezm0x': {
      'ko': '',
      'en': '',
    },
  },
].reduce((a, b) => a..addAll(b));
