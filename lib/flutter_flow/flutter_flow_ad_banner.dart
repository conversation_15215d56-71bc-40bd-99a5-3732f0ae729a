import 'dart:io';

import 'package:flutter/foundation.dart' show kIsWeb, kReleaseMode;
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';
import 'package:my_chef/flutter_flow/internationalization.dart';

class FlutterFlowAdBanner extends StatefulWidget {
  const FlutterFlowAdBanner({
    super.key,
    this.width,
    this.height,
    required this.showsTestAd,
    this.iOSAdUnitID,
    this.androidAdUnitID,
  });

  final double? width;
  final double? height;
  final bool showsTestAd;
  final String? iOSAdUnitID;
  final String? androidAdUnitID;

  @override
  _FlutterFlowAdBannerState createState() => _FlutterFlowAdBannerState();
}

class _FlutterFlowAdBannerState extends State<FlutterFlowAdBanner> {
  static const AdRequest request = AdRequest();

  BannerAd? _anchoredBanner;
  AdWidget? adWidget;

  @override
  void initState() {
    super.initState();

    SchedulerBinding.instance.addPostFrameCallback((_) {
      _createAnchoredBanner(context);
    });
  }

  @override
  void dispose() {
    super.dispose();
    _anchoredBanner?.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return _anchoredBanner != null && adWidget != null
        ? Container(
            alignment: Alignment.center,
            color: Colors.transparent,
            width: _anchoredBanner!.size.width.toDouble(),
            height: _anchoredBanner!.size.height.toDouble(),
            child: adWidget,
          )
        : Container(
            alignment: Alignment.center,
            child: DecoratedBox(
              decoration: BoxDecoration(
                color: Colors.black,
                borderRadius: BorderRadius.circular(8.0),
              ),
              child: Image.asset(
                FFLocalizations.of(context).languageCode == 'ko'
                    ? 'assets/images/ad_loading_banner.png'
                    : 'assets/images/ad_loading_banner_en.png',
                fit: BoxFit.cover,
              ),
            ),
          );
  }

  Future _createAnchoredBanner(BuildContext context) async {
    final AdSize? size = widget.width != null && widget.height != null
        ? AdSize(
            height: widget.height!.toInt(),
            width: widget.width!.toInt(),
          )
        : await AdSize.getAnchoredAdaptiveBannerAdSize(
            widget.width == null ? Orientation.portrait : Orientation.landscape,
            widget.width == null
                ? MediaQuery.sizeOf(context).width.truncate()
                : MediaQuery.sizeOf(context).height.truncate(),
          );

    if (size == null) {
      debugPrint('Unable to get size of anchored banner.');
      return;
    }

    final isAndroid = !kIsWeb && Platform.isAndroid;
    final BannerAd banner = BannerAd(
      size: size,
      request: request,
      adUnitId: widget.showsTestAd
          ? isAndroid
              ? 'ca-app-pub-3940256099942544/6300978111'
              : 'ca-app-pub-3940256099942544/2934735716'
          : isAndroid
              ? widget.androidAdUnitID!
              : widget.iOSAdUnitID!,
      listener: BannerAdListener(
        onAdLoaded: (ad) {
          if (mounted) {
            setState(() => _anchoredBanner = ad as BannerAd);
          }
        },
        onAdFailedToLoad: (ad, error) {
          ad.dispose();
        },
      ),
    );
    if (kReleaseMode) {
      await banner.load();
    }
    // await banner.load();

    adWidget = AdWidget(ad: banner);
    setState(() {});
    return;
  }
}
