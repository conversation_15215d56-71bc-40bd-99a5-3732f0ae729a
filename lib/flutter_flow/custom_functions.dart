import 'dart:convert';
import 'dart:math' as math;

import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';
import 'package:timeago/timeago.dart' as timeago;
import 'lat_lng.dart';
import 'place.dart';
import 'uploaded_file.dart';
import '/backend/schema/structs/index.dart';
import '/backend/supabase/supabase.dart';

List<dynamic> convertJSONToList(dynamic jSONmap) {
  List<dynamic> result = [];

  if (jSONmap != null) {
    for (var jsonObject in jSONmap) {
      result.add(jsonObject);
    }
  }

  return result;
}

String addOne(int num) {
  int resultNum = num + 1;
  String result = resultNum.toString() + ".";

  return result;
}

List<String> makeStepListWithNumber(List<String> step) {
  int i = 1;

  return step.map((e) {
    String modifiedElement = i.toString() + e;
    i++;
    return modifiedElement;
  }).toList();
}

double convertStringToDouble(String str) {
  return double.parse(str);
}
