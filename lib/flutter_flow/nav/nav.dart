import 'dart:async';

import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:page_transition/page_transition.dart';
import 'package:provider/provider.dart';
import 'package:showcaseview/showcaseview.dart';

import '/backend/schema/structs/index.dart';

import '/backend/supabase/supabase.dart';

import '/index.dart';
import '/main.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/lat_lng.dart';
import '/flutter_flow/place.dart';
import '/flutter_flow/flutter_flow_util.dart';
import 'serialization_util.dart';

export 'package:go_router/go_router.dart';
export 'serialization_util.dart';

const kTransitionInfoKey = '__transition_info__';

class AppStateNotifier extends ChangeNotifier {
  AppStateNotifier._();

  static AppStateNotifier? _instance;
  static AppStateNotifier get instance => _instance ??= AppStateNotifier._();

  bool showSplashImage = true;

  void stopShowingSplashImage() {
    showSplashImage = false;
    notifyListeners();
  }
}

GoRouter createRouter(AppStateNotifier appStateNotifier) => GoRouter(
      initialLocation: '/',
      debugLogDiagnostics: true,
      refreshListenable: appStateNotifier,
      errorBuilder: (context, state) => Consumer<AppStateNotifier>(
        builder: (context, appStateNotifier, _) {
          return appStateNotifier.showSplashImage
              ? Container(
                  color: Colors.transparent,
                  child: Image.asset(
                    'assets/images/error_image.png',
                    fit: BoxFit.contain,
                  ),
                )
              : ShowCaseWidget(
                  onFinish: () {
                    FFAppState().mainPageShowCase = false;
                  },
                  builder: (context) => const MainPageWidget(),
                );
        },
      ),
      routes: [
        FFRoute(
          name: '_initialize',
          path: '/',
          builder: (context, _) => Consumer<AppStateNotifier>(
              builder: (context, appStateNotifier, _) {
            return appStateNotifier.showSplashImage
                ? Container(
                    color: Colors.transparent,
                    child: Image.asset(
                      'assets/images/splash.png',
                      fit: BoxFit.contain,
                    ),
                  )
                : ShowCaseWidget(
                    onFinish: () {
                      FFAppState().mainPageShowCase = false;
                    },
                    builder: (context) => const MainPageWidget(),
                  );
          }),
        ),
        FFRoute(
          name: 'RecipeDetailPage',
          path: '/recipeDetailPage',
          builder: (context, params) => RecipeDetailPageWidget(
            title: params.getParam(context, 'title', ParamType.String),
            time: params.getParam(context, 'time', ParamType.String),
            difficulty:
                params.getParam(context, 'difficulty', ParamType.String),
            dscription:
                params.getParam(context, 'dscription', ParamType.String),
            ingredients: params.getParam<String>(
                context, 'ingredients', ParamType.String, true),
            steps: params.getParam<String>(
                context, 'steps', ParamType.String, true),
            isNewRecipe:
                params.getParam(context, 'isNewRecipe', ParamType.bool),
            index: params.getParam(context, 'index', ParamType.int),
          ),
        ),
        FFRoute(
          name: 'RecipeListPage',
          path: '/recipeListPage',
          builder: (context, params) {
            print(params.toString());
            return ShowCaseWidget(
              onFinish: () {
                FFAppState().recipeListPageShowCase = false;
              },
              builder: (context) => RecipeListPageWidget(
                recipeList: params.getParam<dynamic>(
                    context, 'recipeList', ParamType.JSON, true),
                userRecipeListView: params.getParam(
                    context, 'userRecipeListView', ParamType.bool),
                isFirstTimeToStartApp: params.getParam(
                    context, 'isFirstTimeToStartApp', ParamType.bool),
              ),
            );
          },
        ),
        FFRoute(
          name: 'MainPage',
          path: '/mainPage',
          builder: (context, params) => ShowCaseWidget(
            onFinish: () {
              FFAppState().mainPageShowCase = false;
            },
            builder: (context) => const MainPageWidget(),
          ),
        ),
        FFRoute(
          name: 'WriteNewRecipePage',
          path: '/writeNewRecipePage',
          builder: (context, params) => WriteNewRecipePageWidget(
            title: params.getParam(context, 'title', ParamType.String),
            description:
                params.getParam(context, 'description', ParamType.String),
            ingredients: params.getParam<String>(
                context, 'ingredients', ParamType.String, true),
            time: params.getParam(context, 'time', ParamType.String),
            difficulty:
                params.getParam(context, 'difficulty', ParamType.String),
            steps: params.getParam<String>(
                context, 'steps', ParamType.String, true),
            index: params.getParam(context, 'index', ParamType.int),
          ),
        ),
        FFRoute(
          name: 'ModifyRecipePage',
          path: '/modifyRecipePage',
          builder: (context, params) => ModifyRecipePageWidget(
            title: params.getParam(context, 'title', ParamType.String),
            description:
                params.getParam(context, 'description', ParamType.String),
            ingredients: params.getParam<String>(
                context, 'ingredients', ParamType.String, true),
            time: params.getParam(context, 'time', ParamType.String),
            steps: params.getParam<String>(
                context, 'steps', ParamType.String, true),
            index: params.getParam(context, 'index', ParamType.int),
          ),
        )
      ].map((r) => r.toRoute(appStateNotifier)).toList(),
    );

extension NavParamExtensions on Map<String, String?> {
  Map<String, String> get withoutNulls => Map.fromEntries(
        entries
            .where((e) => e.value != null)
            .map((e) => MapEntry(e.key, e.value!)),
      );
}

extension NavigationExtensions on BuildContext {
  void safePop() {
    // If there is only one route on the stack, navigate to the initial
    // page instead of popping.
    if (canPop()) {
      pop();
    } else {
      go('/');
    }
  }
}

extension _GoRouterStateExtensions on GoRouterState {
  Map<String, dynamic> get extraMap =>
      extra != null ? extra as Map<String, dynamic> : {};
  Map<String, dynamic> get allParams => <String, dynamic>{}
    ..addAll(pathParameters)
    ..addAll(queryParameters)
    ..addAll(extraMap);

  TransitionInfo get transitionInfo => extraMap.containsKey(kTransitionInfoKey)
      ? extraMap[kTransitionInfoKey] as TransitionInfo
      : TransitionInfo.appDefault();
}

class FFParameters {
  FFParameters(this.state, [this.asyncParams = const {}]);

  final GoRouterState state;
  final Map<String, Future<dynamic> Function(String)> asyncParams;

  Map<String, dynamic> futureParamValues = {};

  // Parameters are empty if the params map is empty or if the only parameter
  // present is the special extra parameter reserved for the transition info.
  bool get isEmpty =>
      state.allParams.isEmpty ||
      (state.extraMap.length == 1 &&
          state.extraMap.containsKey(kTransitionInfoKey));
  bool isAsyncParam(MapEntry<String, dynamic> param) =>
      asyncParams.containsKey(param.key) && param.value is String;
  bool get hasFutures => state.allParams.entries.any(isAsyncParam);
  Future<bool> completeFutures() => Future.wait(
        state.allParams.entries.where(isAsyncParam).map(
          (param) async {
            final doc = await asyncParams[param.key]!(param.value)
                .onError((_, __) => null);
            if (doc != null) {
              futureParamValues[param.key] = doc;
              return true;
            }
            return false;
          },
        ),
      ).onError((_, __) => [false]).then((v) => v.every((e) => e));

  dynamic getParam<T>(
    BuildContext context,
    String paramName,
    ParamType type, [
    bool isList = false,
  ]) {
    if (futureParamValues.containsKey(paramName)) {
      return futureParamValues[paramName];
    }
    if (!state.allParams.containsKey(paramName)) {
      return null;
    }
    var param = state.allParams[paramName];

    // Got parameter from `extras`, so just directly return it.
    if (param is! String) {
      return param;
    }
    // Return serialized value.
    return deserializeParam<T>(
      param,
      type,
      isList,
    );
  }
}

class FFRoute {
  const FFRoute({
    required this.name,
    required this.path,
    required this.builder,
    this.requireAuth = false,
    this.asyncParams = const {},
    this.routes = const [],
  });

  final String name;
  final String path;
  final bool requireAuth;
  final Map<String, Future<dynamic> Function(String)> asyncParams;
  final Widget Function(BuildContext, FFParameters) builder;
  final List<GoRoute> routes;

  GoRoute toRoute(AppStateNotifier appStateNotifier) => GoRoute(
        name: name,
        path: path,
        pageBuilder: (context, state) {
          final ffParams = FFParameters(state, asyncParams);
          final page = ffParams.hasFutures
              ? FutureBuilder(
                  future: ffParams.completeFutures(),
                  builder: (context, _) => builder(context, ffParams),
                )
              : builder(context, ffParams);
          final child = page;

          final transitionInfo = state.transitionInfo;
          return transitionInfo.hasTransition
              ? CustomTransitionPage(
                  key: state.pageKey,
                  child: child,
                  transitionDuration: transitionInfo.duration,
                  transitionsBuilder:
                      (context, animation, secondaryAnimation, child) =>
                          PageTransition(
                    type: transitionInfo.transitionType,
                    duration: transitionInfo.duration,
                    reverseDuration: transitionInfo.duration,
                    alignment: transitionInfo.alignment,
                    child: child,
                  ).buildTransitions(
                    context,
                    animation,
                    secondaryAnimation,
                    child,
                  ),
                )
              : MaterialPage(key: state.pageKey, child: child);
        },
        routes: routes,
      );
}

class TransitionInfo {
  const TransitionInfo({
    required this.hasTransition,
    this.transitionType = PageTransitionType.fade,
    this.duration = const Duration(milliseconds: 300),
    this.alignment,
  });

  final bool hasTransition;
  final PageTransitionType transitionType;
  final Duration duration;
  final Alignment? alignment;

  static TransitionInfo appDefault() => TransitionInfo(hasTransition: false);
}

class RootPageContext {
  const RootPageContext(this.isRootPage, [this.errorRoute]);
  final bool isRootPage;
  final String? errorRoute;

  static bool isInactiveRootPage(BuildContext context) {
    final rootPageContext = context.read<RootPageContext?>();
    final isRootPage = rootPageContext?.isRootPage ?? false;
    final location = GoRouterState.of(context).location;

    return isRootPage &&
        location != '/' &&
        location != rootPageContext?.errorRoute;
  }

  static Widget wrap(Widget child, {String? errorRoute}) => Provider.value(
        value: RootPageContext(true, errorRoute),
        child: child,
      );
}
