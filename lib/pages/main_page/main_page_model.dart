import '/backend/api_requests/api_calls.dart';
import '/backend/supabase/supabase.dart';
import '/components/alert_dialog/alert_dialog_widget.dart';
import '/components/languages_options_component/languages_options_component_widget.dart';
import '/components/rating_text_component/rating_text_component_widget.dart';
import '/flutter_flow/flutter_flow_ad_banner.dart';
import '/flutter_flow/flutter_flow_animations.dart';
import '/flutter_flow/flutter_flow_autocomplete_options_list.dart';
import '/flutter_flow/flutter_flow_icon_button.dart';
import '/flutter_flow/flutter_flow_radio_button.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/flutter_flow/flutter_flow_widgets.dart';
import '/flutter_flow/form_field_controller.dart';
import '/custom_code/actions/index.dart' as actions;
import 'main_page_widget.dart' show MainPageWidget;
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter/services.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';

class MainPageModel extends FlutterFlowModel<MainPageWidget> {
  ///  Local state fields for this page.

  List<String> ingredients = [];
  void addToIngredients(String item) => ingredients.add(item);
  void removeFromIngredients(String item) => ingredients.remove(item);
  void removeAtIndexFromIngredients(int index) => ingredients.removeAt(index);
  void insertAtIndexInIngredients(int index, String item) =>
      ingredients.insert(index, item);
  void updateIngredientsAtIndex(int index, Function(String) updateFn) =>
      ingredients[index] = updateFn(ingredients[index]);

  bool isFoodNameFieldTapped = false;

  bool isIngredientSearchButtonTapped = false;

  bool isTimesCardTapped = false;

  bool isDifficultyCardTapped = false;

  // showcase keys
  final List<GlobalKey> showcaseKeysList = [
    GlobalKey(), // ingredientField
    GlobalKey(), // ingredientSearchButton
    GlobalKey(), // timesCard
  ];

  ///  State fields for stateful widgets in this page.

  final unfocusNode = FocusNode();
  // Stores action output result for [Backend Call - API (callfeatureflags)] action in MainPage widget.
  ApiCallResponse? callfeatureflagsResult;
  // Stores action output result for [Custom Action - getIngredients] action in MainPage widget.
  List<String>? supabaseIngredientList;
  // State field(s) for SearchTextField widget.
  final searchTextFieldKey = GlobalKey();
  FocusNode? searchTextFieldFocusNode;
  TextEditingController? searchTextFieldController;
  String? searchTextFieldSelectedOption;
  String? Function(BuildContext, String?)? searchTextFieldControllerValidator;
  // State field(s) for TimesRadioButton widget.
  FormFieldController<String>? timesRadioButtonValueController;
  // Model for RatingTextComponent component.
  late RatingTextComponentModel ratingTextComponentModel;
  // State field(s) for RatingBar widget.
  double? ratingBarValue;
  // Stores action output result for [Backend Call - API (callOpenai)] action in searchButton widget.
  ApiCallResponse? getRecipeListResult;

  /// Initialization and disposal methods.

  void initState(BuildContext context) {
    ratingTextComponentModel =
        createModel(context, () => RatingTextComponentModel());
  }

  void dispose() {
    unfocusNode.dispose();
    // searchTextFieldFocusNode?.dispose();

    ratingTextComponentModel.dispose();
  }

  /// Action blocks are added here.

  /// Additional helper methods are added here.

  String? get timesRadioButtonValue => timesRadioButtonValueController?.value;
}
