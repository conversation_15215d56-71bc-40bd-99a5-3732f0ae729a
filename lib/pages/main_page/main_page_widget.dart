import 'package:flutter/foundation.dart';

import '/backend/api_requests/api_calls.dart';
import '/backend/supabase/supabase.dart';
import '/components/alert_dialog/alert_dialog_widget.dart';
import '/components/languages_options_component/languages_options_component_widget.dart';
import '/components/rating_text_component/rating_text_component_widget.dart';
import '/flutter_flow/flutter_flow_ad_banner.dart';
import '/flutter_flow/flutter_flow_animations.dart';
import '/flutter_flow/flutter_flow_autocomplete_options_list.dart';
import '/flutter_flow/flutter_flow_icon_button.dart';
import '/flutter_flow/flutter_flow_radio_button.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/flutter_flow/flutter_flow_widgets.dart';
import '/flutter_flow/form_field_controller.dart';
import '/custom_code/actions/index.dart' as actions;
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter/services.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:showcaseview/showcaseview.dart';
import 'package:provider/provider.dart';
import 'main_page_model.dart';

class MainPageWidget extends StatefulWidget {
  const MainPageWidget({
    super.key,
    this.ingredientList,
  });

  final List<String>? ingredientList;

  @override
  _MainPageWidgetState createState() => _MainPageWidgetState();
}

class _MainPageWidgetState extends State<MainPageWidget>
    with TickerProviderStateMixin {
  late MainPageModel _model;

  final scaffoldKey = GlobalKey<ScaffoldState>();

  final animationsMap = {
    'textFieldOnPageLoadAnimation': AnimationInfo(
      trigger: AnimationTrigger.onPageLoad,
      effects: [
        FadeEffect(
          curve: Curves.easeInOut,
          delay: 0.ms,
          duration: 600.ms,
          begin: 0.0,
          end: 1.0,
        ),
      ],
    ),
    'iconButtonOnPageLoadAnimation1': AnimationInfo(
      trigger: AnimationTrigger.onPageLoad,
      effects: [
        RotateEffect(
          curve: Curves.easeInOut,
          delay: 0.ms,
          duration: 600.ms,
          begin: 0.0,
          end: 1.0,
        ),
      ],
    ),
    'iconButtonOnPageLoadAnimation2': AnimationInfo(
      trigger: AnimationTrigger.onPageLoad,
      effects: [
        RotateEffect(
          curve: Curves.easeInOut,
          delay: 0.ms,
          duration: 600.ms,
          begin: 0.0,
          end: 1.0,
        ),
      ],
    ),
    'iconButtonOnPageLoadAnimation3': AnimationInfo(
      trigger: AnimationTrigger.onPageLoad,
      effects: [
        RotateEffect(
          curve: Curves.easeInOut,
          delay: 0.ms,
          duration: 600.ms,
          begin: 0.0,
          end: 1.0,
        ),
      ],
    ),
    'iconButtonOnPageLoadAnimation4': AnimationInfo(
      trigger: AnimationTrigger.onPageLoad,
      effects: [
        RotateEffect(
          curve: Curves.easeInOut,
          delay: 0.ms,
          duration: 600.ms,
          begin: 0.0,
          end: 1.0,
        ),
      ],
    ),
    'iconButtonOnPageLoadAnimation5': AnimationInfo(
      trigger: AnimationTrigger.onPageLoad,
      effects: [
        RotateEffect(
          curve: Curves.easeInOut,
          delay: 0.ms,
          duration: 600.ms,
          begin: 0.0,
          end: 1.0,
        ),
      ],
    ),
    'iconButtonOnPageLoadAnimation6': AnimationInfo(
      trigger: AnimationTrigger.onPageLoad,
      effects: [
        RotateEffect(
          curve: Curves.easeInOut,
          delay: 0.ms,
          duration: 600.ms,
          begin: 0.0,
          end: 1.0,
        ),
      ],
    ),
  };

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => MainPageModel());

    // On page load action.
    SchedulerBinding.instance.addPostFrameCallback((_) async {
      // GetIngredientsFormSupabaseAction
      _model.supabaseIngredientList = await actions.getIngredients(
        FFLocalizations.of(context).languageCode,
      );
      // UpdateAppIngredientsAction
      setState(() {
        FFAppState().ingredients =
            _model.supabaseIngredientList!.toList().cast<String>();
      });
    });
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (FFAppState().mainPageShowCase) {
        // SetIsFirstTimeToStartAppFalseAction
        ShowCaseWidget.of(context).startShowCase([
          _model.showcaseKeysList[0],
          _model.showcaseKeysList[1],
          _model.showcaseKeysList[2],
        ]);
      }
    });
    _model.searchTextFieldController ??= TextEditingController();
  }

  @override
  void dispose() {
    _model.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (isiOS) {
      SystemChrome.setSystemUIOverlayStyle(
        SystemUiOverlayStyle(
          statusBarBrightness: Theme.of(context).brightness,
          systemStatusBarContrastEnforced: true,
        ),
      );
    }

    context.watch<FFAppState>();

    return GestureDetector(
      onTap: () => _model.unfocusNode.canRequestFocus
          ? FocusScope.of(context).requestFocus(_model.unfocusNode)
          : FocusScope.of(context).unfocus(),
      child: Scaffold(
        key: scaffoldKey,
        backgroundColor: FlutterFlowTheme.of(context).primaryBackground,
        appBar: AppBar(
          backgroundColor: FlutterFlowTheme.of(context).primaryBackground,
          automaticallyImplyLeading: false,
          leading: Padding(
            padding: const EdgeInsetsDirectional.fromSTEB(10, 0, 0, 0),
            child: FlutterFlowIconButton(
              borderRadius: 20,
              borderWidth: 0,
              buttonSize: 40,
              icon: Icon(
                Icons.language_rounded,
                color: FlutterFlowTheme.of(context).tertiary,
                size: 24,
              ),
              onPressed: () async {
                showModalBottomSheet(
                  isScrollControlled: true,
                  backgroundColor: const Color(0x00FFFFFF),
                  enableDrag: false,
                  context: context,
                  builder: (context) {
                    return GestureDetector(
                      onTap: () => _model.unfocusNode.canRequestFocus
                          ? FocusScope.of(context)
                              .requestFocus(_model.unfocusNode)
                          : FocusScope.of(context).unfocus(),
                      child: Padding(
                        padding: MediaQuery.viewInsetsOf(context),
                        child: const LanguagesOptionsComponentWidget(),
                      ),
                    );
                  },
                ).then((value) => safeSetState(() {}));
              },
            ),
          ),
          actions: [
            Padding(
              padding: const EdgeInsetsDirectional.fromSTEB(0, 0, 10, 0),
              child: FlutterFlowIconButton(
                borderColor: Colors.transparent,
                borderRadius: 20,
                borderWidth: 1,
                buttonSize: 40,
                icon: Icon(
                  Icons.menu_book,
                  color: FlutterFlowTheme.of(context).tertiary,
                  size: 24,
                ),
                onPressed: () async {
                  context.pushNamed(
                    'RecipeListPage',
                    queryParameters: {
                      'recipeList': serializeParam(
                        FFAppState()
                            .UserRecipeList
                            .map((e) => e.toMap())
                            .toList(),
                        ParamType.JSON,
                        true,
                      ),
                      'userRecipeListView': serializeParam(
                        true,
                        ParamType.bool,
                      ),
                      'isFirstTimeToStartApp': serializeParam(
                        FFAppState().isFirstTimeToStartApp,
                        ParamType.bool,
                      ),
                    }.withoutNulls,
                    extra: <String, dynamic>{
                      kTransitionInfoKey: const TransitionInfo(
                        hasTransition: true,
                        transitionType: PageTransitionType.rightToLeft,
                      ),
                    },
                  );
                },
              ),
            )
          ],
        ),
        body: SingleChildScrollView(
          padding: const EdgeInsets.all(8.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                constraints: const BoxConstraints(
                  minWidth: double.infinity,
                  minHeight: 80.0,
                  maxWidth: double.infinity,
                  maxHeight: 150.0,
                ),
                decoration: BoxDecoration(
                  image: DecorationImage(
                    fit: BoxFit.cover,
                    image: Image.network(
                      'https://images.unsplash.com/photo-1466637574441-749b8f19452f?crop=entropy&cs=tinysrgb&fit=max&fm=jpg&ixid=M3w0NTYyMDF8MHwxfHNlYXJjaHwyfHxyZWNpcGV8ZW58MHx8fHwxNjkzNDU3MDYxfDA&ixlib=rb-4.0.3&q=80&w=1080',
                    ).image,
                  ),
                  borderRadius: BorderRadius.circular(5.0),
                ),
                child: Align(
                  alignment: const AlignmentDirectional(-1.0, 0.0),
                  child: Padding(
                    padding: const EdgeInsetsDirectional.fromSTEB(
                        10.0, 0.0, 0.0, 0.0),
                    child: Text(
                      FFLocalizations.of(context).getText(
                        '854x0c4z' /* 버려지는 재료가 없게,
                            냉털 */
                        ,
                      ),
                      style:
                          FlutterFlowTheme.of(context).headlineMedium.override(
                                fontFamily: 'Outfit',
                                color: Colors.white,
                              ),
                    ),
                  ),
                ),
              ),
              Column(
                mainAxisSize: MainAxisSize.max,
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: const EdgeInsets.all(10),
                    child: Text(
                      FFLocalizations.of(context).getText(
                        '3g9v3pop' /* 레시피 찾기 */,
                      ),
                      style: FlutterFlowTheme.of(context).titleLarge,
                    ),
                  ),
                  Align(
                    alignment: const AlignmentDirectional(0.0, 0.0),
                    child: Showcase(
                      key: _model.showcaseKeysList[0],
                      description: FFLocalizations.of(context).getText(
                        '1v3z1z5' /* 레시피를 찾기 위해 원하는 재료를 입력해주세요. */,
                      ),
                      targetBorderRadius: BorderRadius.circular(8),
                      child: Card(
                        clipBehavior: Clip.antiAliasWithSaveLayer,
                        color: Colors.white,
                        elevation: 4.0,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12.0),
                        ),
                        child: Padding(
                          padding: const EdgeInsets.all(10.0),
                          child: Column(
                            mainAxisSize: MainAxisSize.max,
                            children: [
                              Padding(
                                padding: const EdgeInsets.all(2.0),
                                child: Column(
                                  children: [
                                    Row(
                                      mainAxisSize: MainAxisSize.max,
                                      children: [
                                        Padding(
                                          padding: const EdgeInsetsDirectional
                                              .fromSTEB(0.0, 0.0, 10.0, 0.0),
                                          child: Icon(
                                            Icons.rice_bowl,
                                            color: FlutterFlowTheme.of(context)
                                                .tertiary,
                                            size: 20.0,
                                          ),
                                        ),
                                        Text(
                                          FFLocalizations.of(context).getText(
                                            'h7sdoxgz' /* 재료 */,
                                          ),
                                          style: FlutterFlowTheme.of(context)
                                              .bodyLarge
                                              .override(
                                                fontFamily: 'Readex Pro',
                                                fontWeight: FontWeight.w600,
                                              ),
                                        ),
                                      ],
                                    ),
                                    Row(
                                      mainAxisSize: MainAxisSize.max,
                                      children: [
                                        Expanded(
                                          child: FutureBuilder<
                                              List<IngredientRow>>(
                                            future: IngredientTable().queryRows(
                                              queryFn: (q) => q,
                                              limit: 5,
                                            ),
                                            builder: (context, snapshot) {
                                              if (!snapshot.hasData) {
                                                return Center(
                                                  child: SizedBox(
                                                    width: 50.0,
                                                    height: 50.0,
                                                    child:
                                                        CircularProgressIndicator(
                                                      valueColor:
                                                          AlwaysStoppedAnimation<
                                                              Color>(
                                                        FlutterFlowTheme.of(
                                                                context)
                                                            .primary,
                                                      ),
                                                    ),
                                                  ),
                                                );
                                              }
                                              List<IngredientRow>
                                                  searchTextFieldIngredientRowList =
                                                  snapshot.data!;
                                              return Autocomplete<String>(
                                                initialValue:
                                                    const TextEditingValue(),
                                                optionsBuilder:
                                                    (textEditingValue) {
                                                  if (textEditingValue.text ==
                                                      '') {
                                                    return const Iterable<
                                                        String>.empty();
                                                  }
                                                  return FFAppState()
                                                      .ingredients
                                                      .where((option) {
                                                    final lowercaseOption =
                                                        option.toLowerCase();
                                                    return lowercaseOption
                                                        .contains(
                                                            textEditingValue
                                                                .text
                                                                .toLowerCase());
                                                  });
                                                },
                                                optionsViewBuilder: (context,
                                                    onSelected, options) {
                                                  return AutocompleteOptionsList(
                                                    textFieldKey: _model
                                                        .searchTextFieldKey,
                                                    textController: _model
                                                        .searchTextFieldController!,
                                                    options: options.toList(),
                                                    onSelected: onSelected,
                                                    textStyle:
                                                        FlutterFlowTheme.of(
                                                                context)
                                                            .bodyMedium,
                                                    textHighlightStyle:
                                                        const TextStyle(),
                                                    elevation: 4.0,
                                                    optionBackgroundColor:
                                                        FlutterFlowTheme.of(
                                                                context)
                                                            .primaryBackground,
                                                    optionHighlightColor:
                                                        FlutterFlowTheme.of(
                                                                context)
                                                            .secondaryBackground,
                                                    maxHeight: 200.0,
                                                  );
                                                },
                                                onSelected:
                                                    (String selection) async {
                                                  if (FFAppState()
                                                          .ingredients
                                                          .contains(
                                                              selection) ==
                                                      true) {
                                                    if (_model.ingredients
                                                            .length >=
                                                        5) {
                                                      await showDialog(
                                                        context: context,
                                                        builder:
                                                            (dialogContext) {
                                                          return Dialog(
                                                            insetPadding:
                                                                EdgeInsets.zero,
                                                            backgroundColor:
                                                                Colors
                                                                    .transparent,
                                                            alignment: const AlignmentDirectional(
                                                                    0.0, 0.0)
                                                                .resolve(
                                                                    Directionality.of(
                                                                        context)),
                                                            child:
                                                                GestureDetector(
                                                              onTap: () => _model
                                                                      .unfocusNode
                                                                      .canRequestFocus
                                                                  ? FocusScope.of(
                                                                          context)
                                                                      .requestFocus(
                                                                          _model
                                                                              .unfocusNode)
                                                                  : FocusScope.of(
                                                                          context)
                                                                      .unfocus(),
                                                              child: SizedBox(
                                                                height: MediaQuery.sizeOf(
                                                                            context)
                                                                        .height *
                                                                    0.3,
                                                                width: MediaQuery.sizeOf(
                                                                            context)
                                                                        .width *
                                                                    0.8,
                                                                child:
                                                                    AlertDialogWidget(
                                                                  title: FFLocalizations.of(
                                                                          context)
                                                                      .getText(
                                                                    'klz6536r' /* 입력 가능 재료 개수 초과 */,
                                                                  ),
                                                                  context: FFLocalizations.of(
                                                                          context)
                                                                      .getText(
                                                                    'kvdx91bj' /* 입력 가능한 재료의 총 갯수는 5개 이하입니다. */,
                                                                  ),
                                                                ),
                                                              ),
                                                            ),
                                                          );
                                                        },
                                                      ).then((value) =>
                                                          setState(() {}));
                                                    } else {
                                                      // addIngredientAction
                                                      setState(() {
                                                        _model.addToIngredients(
                                                            _model
                                                                .searchTextFieldController
                                                                .text);
                                                      });
                                                      // ResetFieldAction
                                                      setState(() {
                                                        _model
                                                            .searchTextFieldController
                                                            ?.clear();
                                                      });
                                                    }
                                                  }
                                                },
                                                fieldViewBuilder: (
                                                  context,
                                                  textEditingController,
                                                  focusNode,
                                                  onEditingComplete,
                                                ) {
                                                  _model.searchTextFieldFocusNode =
                                                      focusNode;

                                                  _model.searchTextFieldController =
                                                      textEditingController;
                                                  return TextFormField(
                                                    key: _model
                                                        .searchTextFieldKey,
                                                    controller:
                                                        textEditingController,
                                                    focusNode: focusNode,
                                                    onEditingComplete:
                                                        onEditingComplete,
                                                    autofocus: false,
                                                    textInputAction:
                                                        TextInputAction.done,
                                                    obscureText: false,
                                                    decoration: InputDecoration(
                                                      isDense: true,
                                                      hintText:
                                                          FFLocalizations.of(
                                                                  context)
                                                              .getText(
                                                        'bk9fkhho' /* 재료 검색 */,
                                                      ),
                                                      hintStyle:
                                                          FlutterFlowTheme.of(
                                                                  context)
                                                              .labelMedium
                                                              .override(
                                                                fontFamily:
                                                                    'Readex Pro',
                                                                fontSize: 14.0,
                                                              ),
                                                      enabledBorder:
                                                          UnderlineInputBorder(
                                                        borderSide: BorderSide(
                                                          color: FlutterFlowTheme
                                                                  .of(context)
                                                              .tertiary,
                                                          width: 2.0,
                                                        ),
                                                        borderRadius:
                                                            BorderRadius
                                                                .circular(8.0),
                                                      ),
                                                      focusedBorder:
                                                          UnderlineInputBorder(
                                                        borderSide: BorderSide(
                                                          color: FlutterFlowTheme
                                                                  .of(context)
                                                              .tertiary,
                                                          width: 2.0,
                                                        ),
                                                        borderRadius:
                                                            BorderRadius
                                                                .circular(8.0),
                                                      ),
                                                      errorBorder:
                                                          UnderlineInputBorder(
                                                        borderSide: BorderSide(
                                                          color: FlutterFlowTheme
                                                                  .of(context)
                                                              .error,
                                                          width: 2.0,
                                                        ),
                                                        borderRadius:
                                                            BorderRadius
                                                                .circular(8.0),
                                                      ),
                                                      focusedErrorBorder:
                                                          UnderlineInputBorder(
                                                        borderSide: BorderSide(
                                                          color: FlutterFlowTheme
                                                                  .of(context)
                                                              .error,
                                                          width: 2.0,
                                                        ),
                                                        borderRadius:
                                                            BorderRadius
                                                                .circular(8.0),
                                                      ),
                                                    ),
                                                    style: FlutterFlowTheme.of(
                                                            context)
                                                        .bodyMedium
                                                        .override(
                                                          fontFamily:
                                                              'Readex Pro',
                                                          fontSize: 14.0,
                                                        ),
                                                    textAlign: TextAlign.start,
                                                    cursorColor:
                                                        FlutterFlowTheme.of(
                                                                context)
                                                            .tertiary,
                                                    validator: _model
                                                        .searchTextFieldControllerValidator
                                                        .asValidator(context),
                                                  );
                                                },
                                              ).animateOnPageLoad(animationsMap[
                                                  'textFieldOnPageLoadAnimation']!);
                                            },
                                          ),
                                        ),
                                        FlutterFlowIconButton(
                                          borderRadius: 20.0,
                                          borderWidth: 1.0,
                                          buttonSize: 40.0,
                                          icon: Icon(
                                            Icons.close_rounded,
                                            color: FlutterFlowTheme.of(context)
                                                .tertiary,
                                            size: 20.0,
                                          ),
                                          onPressed: () async {
                                            setState(() {
                                              _model.searchTextFieldController
                                                  ?.clear();
                                            });
                                          },
                                        ).animateOnPageLoad(animationsMap[
                                            'iconButtonOnPageLoadAnimation2']!),
                                      ],
                                    ),
                                  ],
                                ),
                              ),
                              Padding(
                                padding: const EdgeInsetsDirectional.fromSTEB(
                                    3, 10, 3, 10),
                                child: Builder(
                                  builder: (context) {
                                    final ingredientResults = _model.ingredients
                                        .toList()
                                        .take(5)
                                        .toList();
                                    return Wrap(
                                      spacing: 5.0,
                                      runSpacing: 5.0,
                                      alignment: WrapAlignment.start,
                                      crossAxisAlignment:
                                          WrapCrossAlignment.start,
                                      direction: Axis.horizontal,
                                      runAlignment: WrapAlignment.start,
                                      verticalDirection: VerticalDirection.down,
                                      clipBehavior: Clip.none,
                                      children: List.generate(
                                          ingredientResults.length,
                                          (ingredientResultsIndex) {
                                        final ingredientResultsItem =
                                            ingredientResults[
                                                ingredientResultsIndex];
                                        return FFButtonWidget(
                                          onPressed: () async {
                                            setState(() {
                                              _model.removeFromIngredients(
                                                  ingredientResultsItem);
                                            });
                                          },
                                          text: ingredientResultsItem,
                                          icon: Icon(
                                            Icons.close_rounded,
                                            color: FlutterFlowTheme.of(context)
                                                .tertiary,
                                            size: 10.0,
                                          ),
                                          options: FFButtonOptions(
                                            height: 35.0,
                                            padding: const EdgeInsets.all(10.0),
                                            iconPadding:
                                                const EdgeInsets.all(0.0),
                                            color: Colors.white,
                                            textStyle:
                                                FlutterFlowTheme.of(context)
                                                    .labelSmall
                                                    .override(
                                                      fontFamily: 'Readex Pro',
                                                      color:
                                                          FlutterFlowTheme.of(
                                                                  context)
                                                              .tertiary,
                                                    ),
                                            elevation: 1.0,
                                            borderSide: BorderSide(
                                              color:
                                                  FlutterFlowTheme.of(context)
                                                      .tertiary,
                                              width: 1.0,
                                            ),
                                            borderRadius:
                                                BorderRadius.circular(10.0),
                                          ),
                                        );
                                      }),
                                    );
                                  },
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                  Row(
                    mainAxisSize: MainAxisSize.max,
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child: Showcase(
                          key: _model.showcaseKeysList[1],
                          description: FFLocalizations.of(context).getText(
                            '1v3z1z6' /* 원하는 조리 시간대를 선택해주세요. */,
                          ),
                          targetBorderRadius: BorderRadius.circular(8),
                          child: Card(
                            color: Colors.white,
                            elevation: 4.0,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12.0),
                            ),
                            child: Padding(
                              padding: const EdgeInsetsDirectional.fromSTEB(
                                  5.0, 15.0, 5.0, 10.0),
                              child: Column(
                                mainAxisSize: MainAxisSize.max,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Padding(
                                    padding: const EdgeInsets.all(8.0),
                                    child: Row(
                                      mainAxisSize: MainAxisSize.min,
                                      mainAxisAlignment:
                                          MainAxisAlignment.start,
                                      children: [
                                        Padding(
                                          padding: const EdgeInsetsDirectional
                                              .fromSTEB(0.0, 0.0, 10.0, 0.0),
                                          child: Icon(
                                            Icons.access_time_rounded,
                                            color: FlutterFlowTheme.of(context)
                                                .tertiary,
                                            size: 20.0,
                                          ),
                                        ),
                                        Text(
                                          valueOrDefault<String>(
                                            _model.timesRadioButtonValue,
                                            FFLocalizations.of(context).getText(
                                              '1nmgjrot' /* 10-20 분 */,
                                            ),
                                          ),
                                          style: FlutterFlowTheme.of(context)
                                              .bodyLarge
                                              .override(
                                                fontFamily: 'Readex Pro',
                                                fontWeight: FontWeight.w600,
                                              ),
                                        ),
                                      ],
                                    ),
                                  ),
                                  FlutterFlowRadioButton(
                                    options: [
                                      FFLocalizations.of(context).getText(
                                        '1nmgjrot' /* 10-20 분 */,
                                      ),
                                      FFLocalizations.of(context).getText(
                                        'd69mi3j5' /* 20-30 분 */,
                                      ),
                                      FFLocalizations.of(context).getText(
                                        'nik1ib1x' /* 30-45 분 */,
                                      ),
                                      FFLocalizations.of(context).getText(
                                        'ok2zhnac' /* 45분 이상 */,
                                      )
                                    ].toList(),
                                    onChanged: (val) => setState(() {}),
                                    controller: _model
                                            .timesRadioButtonValueController ??=
                                        FormFieldController<String>(
                                            FFLocalizations.of(context).getText(
                                      'c4k8i2wg' /* 10-20 분 */,
                                    )),
                                    optionHeight: 32.0,
                                    textStyle: FlutterFlowTheme.of(context)
                                        .labelMedium,
                                    selectedTextStyle:
                                        FlutterFlowTheme.of(context).bodyMedium,
                                    buttonPosition: RadioButtonPosition.left,
                                    direction: Axis.vertical,
                                    radioButtonColor:
                                        FlutterFlowTheme.of(context).tertiary,
                                    inactiveRadioButtonColor:
                                        FlutterFlowTheme.of(context)
                                            .secondaryText,
                                    toggleable: false,
                                    horizontalAlignment: WrapAlignment.start,
                                    verticalAlignment: WrapCrossAlignment.start,
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                  Builder(
                    builder: (context) => Padding(
                      padding: const EdgeInsetsDirectional.fromSTEB(
                          0.0, 10.0, 0.0, 0.0),
                      child: Showcase(
                        key: _model.showcaseKeysList[2],
                        description: FFLocalizations.of(context).getText(
                          '1v3z1z8' /* 검색 버튼을 눌러 레시피를 찾아보세요. */,
                        ),
                        targetBorderRadius: BorderRadius.circular(8),
                        child: FFButtonWidget(
                          onPressed: () async {
                            if (_model.ingredients.isEmpty) {
                              // ingredientsValidAction
                              await showDialog(
                                context: context,
                                builder: (alertDialogContext) {
                                  return AlertDialog(
                                    title: Text(FFLocalizations.of(context)
                                                .languageCode ==
                                            'en'
                                        ? 'Search Error'
                                        : '검색오류'),
                                    content: Text(FFLocalizations.of(context)
                                                .languageCode ==
                                            'en'
                                        ? 'Please enter the ingredients.'
                                        : '재료를 입력해주세요'),
                                    actions: [
                                      TextButton(
                                        onPressed: () =>
                                            Navigator.pop(alertDialogContext),
                                        child: const Text('Ok'),
                                      ),
                                    ],
                                  );
                                },
                              );
                            } else {
                              // getRecipeListAPICallAction
                              _model.getRecipeListResult =
                                  await CallOpenaiCall.call(
                                ingredientsList: _model.ingredients,
                                time: valueOrDefault<String>(
                                    _model.timesRadioButtonValue,
                                    FFLocalizations.of(context)
                                        .getText('c4k8i2wg')),
                                difficulty: valueOrDefault<String>(
                                  _model.ratingBarValue?.toString(),
                                  '3',
                                ),
                                version: 'production',
                                purpose: 'getRecipeList',
                                language:
                                    FFLocalizations.of(context).languageCode,
                              );
                              if ((_model.getRecipeListResult?.succeeded ??
                                  true)) {
                                for (var i = 0;
                                    i < _model.ingredients.length;
                                    i++) {
                                  actions.setFrequency(
                                    _model.ingredients[i],
                                    FFLocalizations.of(context).languageCode,
                                  );
                                }

                                // pageResetAction
                                setState(() {
                                  _model.ingredients = [];
                                  _model.isTimesCardTapped = false;
                                  _model.isDifficultyCardTapped = false;
                                });
                                // navToRecipeListAction

                                context.goNamed(
                                  'RecipeListPage',
                                  queryParameters: {
                                    'recipeList': serializeParam(
                                      (_model.getRecipeListResult?.jsonBody ??
                                          ''),
                                      ParamType.JSON,
                                      true,
                                    ),
                                    'userRecipeListView': serializeParam(
                                      false,
                                      ParamType.bool,
                                    ),
                                    'isFirstTimeToStartApp': serializeParam(
                                      FFAppState().isFirstTimeToStartApp,
                                      ParamType.bool,
                                    ),
                                  }.withoutNulls,
                                );
                              } else {
                                // netwrokErrorAction
                                await showDialog(
                                  context: context,
                                  builder: (dialogContext) {
                                    return Dialog(
                                      insetPadding: EdgeInsets.zero,
                                      backgroundColor: Colors.transparent,
                                      alignment: const AlignmentDirectional(
                                              0.0, 0.0)
                                          .resolve(Directionality.of(context)),
                                      child: GestureDetector(
                                        onTap: () => _model
                                                .unfocusNode.canRequestFocus
                                            ? FocusScope.of(context)
                                                .requestFocus(
                                                    _model.unfocusNode)
                                            : FocusScope.of(context).unfocus(),
                                        child: AlertDialogWidget(
                                          title: FFLocalizations.of(context)
                                              .getText(
                                            'xxa93fwc' /* 오류메세지 */,
                                          ),
                                          context: FFLocalizations.of(context)
                                              .getText(
                                            'ou4fq465' /* 일시적인 네트워크 오류가 발생했습니다. 재시도 해주세요... */,
                                          ),
                                          ok: FFLocalizations.of(context)
                                              .getText(
                                            'dxf0jtxw' /* 확인 */,
                                          ),
                                        ),
                                      ),
                                    );
                                  },
                                );
                              }
                            }
                            setState(() {});
                          },
                          text: FFLocalizations.of(context).getText(
                            'd66z1f48' /* 검색 */,
                          ),
                          options: FFButtonOptions(
                            width: double.infinity,
                            height: 40.0,
                            padding: const EdgeInsetsDirectional.fromSTEB(
                                24.0, 0.0, 24.0, 0.0),
                            iconPadding: const EdgeInsetsDirectional.fromSTEB(
                                0.0, 0.0, 0.0, 0.0),
                            color: FlutterFlowTheme.of(context).tertiary,
                            textStyle: FlutterFlowTheme.of(context)
                                .titleSmall
                                .override(
                                  fontFamily: 'Readex Pro',
                                  color: Colors.white,
                                ),
                            elevation: 3.0,
                            borderSide: const BorderSide(
                              color: Colors.transparent,
                              width: 1.0,
                            ),
                            borderRadius: BorderRadius.circular(8.0),
                          ),
                        ),
                      ),
                    ),
                  ),
                  const Padding(
                    padding:
                        EdgeInsetsDirectional.fromSTEB(0.0, 50.0, 0.0, 5.0),
                    child: FlutterFlowAdBanner(
                      height: 50.0,
                      showsTestAd: !kReleaseMode,
                      iOSAdUnitID: 'ca-app-pub-2947221843793912/3023158701',
                      androidAdUnitID: 'ca-app-pub-2947221843793912/6708951022',
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
