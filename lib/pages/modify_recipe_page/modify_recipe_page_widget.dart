import '/backend/schema/structs/index.dart';
import '/components/alert_dialog/alert_dialog_widget.dart';
import '/components/recipe_form/recipe_form_widget.dart';
import '/flutter_flow/flutter_flow_icon_button.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/flutter_flow/flutter_flow_widgets.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import 'modify_recipe_page_model.dart';
export 'modify_recipe_page_model.dart';

class ModifyRecipePageWidget extends StatefulWidget {
  const ModifyRecipePageWidget({
    Key? key,
    String? title,
    String? description,
    this.ingredients,
    String? time,
    this.steps,
    int? index,
  })  : this.title = title ?? '',
        this.description = description ?? '',
        this.time = time ?? '',
        this.index = index ?? 0,
        super(key: key);

  final String title;
  final String description;
  final List<String>? ingredients;
  final String time;
  final List<String>? steps;
  final int index;

  @override
  _ModifyRecipePageWidgetState createState() => _ModifyRecipePageWidgetState();
}

class _ModifyRecipePageWidgetState extends State<ModifyRecipePageWidget> {
  late ModifyRecipePageModel _model;

  final scaffoldKey = GlobalKey<ScaffoldState>();

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => ModifyRecipePageModel());
  }

  @override
  void dispose() {
    _model.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (isiOS) {
      SystemChrome.setSystemUIOverlayStyle(
        SystemUiOverlayStyle(
          statusBarBrightness: Theme.of(context).brightness,
          systemStatusBarContrastEnforced: true,
        ),
      );
    }

    context.watch<FFAppState>();

    return GestureDetector(
      onTap: () => _model.unfocusNode.canRequestFocus
          ? FocusScope.of(context).requestFocus(_model.unfocusNode)
          : FocusScope.of(context).unfocus(),
      child: Scaffold(
        key: scaffoldKey,
        backgroundColor: Colors.white,
        appBar: AppBar(
          backgroundColor: FlutterFlowTheme.of(context).secondaryBackground,
          automaticallyImplyLeading: false,
          leading: FlutterFlowIconButton(
            borderColor: Colors.transparent,
            borderRadius: 30.0,
            borderWidth: 1.0,
            buttonSize: 60.0,
            icon: Icon(
              Icons.arrow_back_rounded,
              color: Colors.black,
              size: 30.0,
            ),
            onPressed: () async {
              context.pop();
            },
          ),
          title: Text(
            FFLocalizations.of(context).getText(
              'xbgmorzj' /* 레시피 수정 */,
            ),
            style: FlutterFlowTheme.of(context).headlineMedium.override(
                  fontFamily: 'Outfit',
                  color: FlutterFlowTheme.of(context).primaryText,
                  fontSize: 22.0,
                ),
          ),
          actions: [],
          centerTitle: false,
          elevation: 2.0,
        ),
        body: SafeArea(
          top: true,
          child: Column(
            mainAxisSize: MainAxisSize.max,
            children: [
              Expanded(
                child: wrapWithModel(
                  model: _model.recipeFormModel,
                  updateCallback: () => setState(() {}),
                  child: RecipeFormWidget(
                    title: widget.title,
                    description: widget.description,
                    ingredients: widget.ingredients,
                    index: widget.index,
                    steps: widget.steps,
                    time: widget.time,
                  ),
                ),
              ),
              Padding(
                padding: EdgeInsets.all(5.0),
                child: Container(
                  width: double.infinity,
                  child: Stack(
                    children: [
                      if (widget.title != null && widget.title != '')
                        Builder(
                          builder: (context) => FFButtonWidget(
                            onPressed: () async {
                              if ((_model.recipeFormModel
                                              .titleTextFieldController.text !=
                                          null &&
                                      _model.recipeFormModel
                                              .titleTextFieldController.text !=
                                          '') &&
                                  ((_model.recipeFormModel.steps.isNotEmpty) ==
                                      true) &&
                                  ((_model.recipeFormModel.ingredients
                                          .isNotEmpty) ==
                                      true)) {
                                // modifyRecipeAction
                                setState(() {
                                  FFAppState().updateUserRecipeListAtIndex(
                                    widget.index,
                                    (e) => e
                                      ..title = _model.recipeFormModel
                                          .titleTextFieldController.text
                                      ..time = _model
                                          .recipeFormModel.timesRadioButtonValue
                                      ..description = _model.recipeFormModel
                                          .descriptionTextFieldController.text
                                      ..steps =
                                          _model.recipeFormModel.steps.toList()
                                      ..ingredients = _model
                                          .recipeFormModel.ingredients
                                          .toList()
                                      ..difficulty = _model
                                          .recipeFormModel.ratingBarValue
                                          ?.toString(),
                                  );
                                });
                                // showModifyResultAction
                                ScaffoldMessenger.of(context).showSnackBar(
                                  SnackBar(
                                    content: Text(
                                      FFLocalizations.of(context)
                                                  .languageCode ==
                                              'en'
                                          ? 'The ${_model.recipeFormModel.titleTextFieldController.text} recipe has been updated.'
                                          : '${_model.recipeFormModel.titleTextFieldController.text} 레시피가 수정 되었습니다.',
                                      style: TextStyle(
                                        color: FlutterFlowTheme.of(context)
                                            .secondaryBackground,
                                      ),
                                    ),
                                    duration: Duration(milliseconds: 3000),
                                    backgroundColor:
                                        FlutterFlowTheme.of(context).tertiary,
                                  ),
                                );
                                // navToRecipeListPage
                                if (Navigator.of(context).canPop()) {
                                  context.pop();
                                }
                                context.pushNamed(
                                  'RecipeListPage',
                                  queryParameters: {
                                    'recipeList': serializeParam(
                                      FFAppState()
                                          .UserRecipeList
                                          .map((e) => e.toMap())
                                          .toList(),
                                      ParamType.JSON,
                                      true,
                                    ),
                                    'userRecipeListView': serializeParam(
                                      true,
                                      ParamType.bool,
                                    ),
                                  }.withoutNulls,
                                );
                              } else {
                                // invalidInputAction
                                await showDialog(
                                  context: context,
                                  builder: (dialogContext) {
                                    return Dialog(
                                      insetPadding: EdgeInsets.zero,
                                      backgroundColor: Colors.transparent,
                                      alignment: AlignmentDirectional(0.0, 0.0)
                                          .resolve(Directionality.of(context)),
                                      child: GestureDetector(
                                        onTap: () => _model
                                                .unfocusNode.canRequestFocus
                                            ? FocusScope.of(context)
                                                .requestFocus(
                                                    _model.unfocusNode)
                                            : FocusScope.of(context).unfocus(),
                                        child: AlertDialogWidget(
                                          title: FFLocalizations.of(context)
                                              .getText(
                                            'j4yttsc8' /* 필수입력 항목 누락 */,
                                          ),
                                          context: FFLocalizations.of(context)
                                              .getText(
                                            'pf4mxj5n' /* 레시피의 제목, 재료, 단계는 필수적으로 기입해주세요. */,
                                          ),
                                          ok: FFLocalizations.of(context)
                                              .getText(
                                            'ukgko6lj' /* 확인 */,
                                          ),
                                        ),
                                      ),
                                    );
                                  },
                                ).then((value) => setState(() {}));
                              }
                            },
                            text: FFLocalizations.of(context).getText(
                              'a5yyztc8' /* 수정 */,
                            ),
                            options: FFButtonOptions(
                              width: double.infinity,
                              height: 40.0,
                              padding: EdgeInsetsDirectional.fromSTEB(
                                  24.0, 0.0, 24.0, 0.0),
                              iconPadding: EdgeInsetsDirectional.fromSTEB(
                                  0.0, 0.0, 0.0, 0.0),
                              color: FlutterFlowTheme.of(context).tertiary,
                              textStyle: FlutterFlowTheme.of(context)
                                  .titleSmall
                                  .override(
                                    fontFamily: 'Readex Pro',
                                    color: Colors.white,
                                  ),
                              elevation: 3.0,
                              borderSide: BorderSide(
                                color: Colors.transparent,
                                width: 1.0,
                              ),
                              borderRadius: BorderRadius.circular(8.0),
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
