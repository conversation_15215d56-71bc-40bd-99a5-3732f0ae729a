import '/backend/schema/structs/index.dart';
import '/components/alert_dialog/alert_dialog_widget.dart';
import '/components/recipe_form/recipe_form_widget.dart';
import '/flutter_flow/flutter_flow_icon_button.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/flutter_flow/flutter_flow_widgets.dart';
import 'modify_recipe_page_widget.dart' show ModifyRecipePageWidget;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';

class ModifyRecipePageModel extends FlutterFlowModel<ModifyRecipePageWidget> {
  ///  Local state fields for this page.

  List<String> ingredients = [];
  void addToIngredients(String item) => ingredients.add(item);
  void removeFromIngredients(String item) => ingredients.remove(item);
  void removeAtIndexFromIngredients(int index) => ingredients.removeAt(index);
  void insertAtIndexInIngredients(int index, String item) =>
      ingredients.insert(index, item);
  void updateIngredientsAtIndex(int index, Function(String) updateFn) =>
      ingredients[index] = updateFn(ingredients[index]);

  String time = '10~15분';

  int difficulty = 3;

  List<String> steps = [];
  void addToSteps(String item) => steps.add(item);
  void removeFromSteps(String item) => steps.remove(item);
  void removeAtIndexFromSteps(int index) => steps.removeAt(index);
  void insertAtIndexInSteps(int index, String item) =>
      steps.insert(index, item);
  void updateStepsAtIndex(int index, Function(String) updateFn) =>
      steps[index] = updateFn(steps[index]);

  bool isTitleCardTapped = false;

  bool isTimesCardTapped = false;

  bool isDifficultyCardTapped = false;

  bool isIngredientSearchButtonTapped = false;

  bool isStepCardTapped = false;

  ///  State fields for stateful widgets in this page.

  final unfocusNode = FocusNode();
  // Model for RecipeForm component.
  late RecipeFormModel recipeFormModel;

  /// Initialization and disposal methods.

  void initState(BuildContext context) {
    recipeFormModel = createModel(context, () => RecipeFormModel());
  }

  void dispose() {
    unfocusNode.dispose();
    recipeFormModel.dispose();
  }

  /// Action blocks are added here.

  /// Additional helper methods are added here.
}
