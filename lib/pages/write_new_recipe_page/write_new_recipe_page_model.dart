import '/backend/schema/structs/index.dart';
import '/components/alert_dialog/alert_dialog_widget.dart';
import '/components/recipe_form/recipe_form_widget.dart';
import '/flutter_flow/flutter_flow_icon_button.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/flutter_flow/flutter_flow_widgets.dart';
import 'write_new_recipe_page_widget.dart' show WriteNewRecipePageWidget;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';

class WriteNewRecipePageModel
    extends FlutterFlowModel<WriteNewRecipePageWidget> {
  ///  State fields for stateful widgets in this page.

  final unfocusNode = FocusNode();
  // Model for RecipeForm component.
  late RecipeFormModel recipeFormModel;

  /// Initialization and disposal methods.

  void initState(BuildContext context) {
    recipeFormModel = createModel(context, () => RecipeFormModel());
  }

  void dispose() {
    unfocusNode.dispose();
    recipeFormModel.dispose();
  }

  /// Action blocks are added here.

  /// Additional helper methods are added here.
}
