import 'package:flutter/foundation.dart';
import 'package:showcaseview/showcaseview.dart';
import 'package:unsplash_client/unsplash_client.dart';

import '/backend/api_requests/api_calls.dart';
import '/backend/schema/structs/index.dart';
import '/flutter_flow/flutter_flow_animations.dart';
import '/flutter_flow/flutter_flow_icon_button.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/flutter_flow/flutter_flow_widgets.dart';
import '/backend/schema/structs/index.dart';
import '/flutter_flow/admob_util.dart' as admob;
import '/flutter_flow/custom_functions.dart' as functions;
import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter/services.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:lottie/lottie.dart';
import 'package:provider/provider.dart';
import 'recipe_list_page_model.dart';
export 'recipe_list_page_model.dart';

class RecipeListPageWidget extends StatefulWidget {
  const RecipeListPageWidget({
    super.key,
    required this.recipeList,
    required this.userRecipeListView,
    required this.isFirstTimeToStartApp,
  });

  final List<dynamic>? recipeList;
  final bool? userRecipeListView;
  final bool? isFirstTimeToStartApp;

  @override
  _RecipeListPageWidgetState createState() => _RecipeListPageWidgetState();
}

class _RecipeListPageWidgetState extends State<RecipeListPageWidget>
    with TickerProviderStateMixin {
  late RecipeListPageModel _model;

  final scaffoldKey = GlobalKey<ScaffoldState>();

  final animationsMap = {
    'iconButtonOnPageLoadAnimation': AnimationInfo(
      trigger: AnimationTrigger.onPageLoad,
      effects: [
        RotateEffect(
          curve: Curves.easeInOut,
          delay: 0.ms,
          duration: 600.ms,
          begin: 0.0,
          end: 1.0,
        ),
      ],
    ),
  };

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => RecipeListPageModel());

    // On page load action.
    SchedulerBinding.instance.addPostFrameCallback((_) async {
      try {
        if (widget.userRecipeListView != null && widget.userRecipeListView!) {
        } else {
          // LoadInterstitialAdAction
          admob.loadInterstitialAd(
            "ca-app-pub-2947221843793912/**********",
            "ca-app-pub-2947221843793912/**********",
            !kReleaseMode,
          );
          if (FFAppState().recipeListPageShowCase) {
            // SetIsFirstTimeToStartAppFalseAction
            ShowCaseWidget.of(context).startShowCase(_model.showcaseKeysList);
          }
        }
      } catch (e) {
        print('An error occurred while loading the interstitial ad: $e');
      }
      // LoadInterstitialAdAction
    });
  }

  @override
  void dispose() {
    _model.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (isiOS) {
      SystemChrome.setSystemUIOverlayStyle(
        SystemUiOverlayStyle(
          statusBarBrightness: Theme.of(context).brightness,
          systemStatusBarContrastEnforced: true,
        ),
      );
    }

    context.watch<FFAppState>();

    return GestureDetector(
      onTap: () => _model.unfocusNode.canRequestFocus
          ? FocusScope.of(context).requestFocus(_model.unfocusNode)
          : FocusScope.of(context).unfocus(),
      child: Scaffold(
        key: scaffoldKey,
        backgroundColor: const Color(0xFFF1F5F8),
        appBar: AppBar(
          backgroundColor: Colors.white,
          automaticallyImplyLeading: false,
          leading: Visibility(
            visible: _model.isNetworkingOver,
            child: FlutterFlowIconButton(
              borderColor: Colors.white,
              borderRadius: 30.0,
              borderWidth: 1.0,
              buttonSize: 60.0,
              fillColor: Colors.white,
              icon: const Icon(
                Icons.arrow_back_rounded,
                color: Colors.black,
                size: 30.0,
              ),
              onPressed: () async {
                // navToMainPageAction
                context.pushNamed('MainPage');
              },
            ),
          ),
          actions: const [],
          centerTitle: false,
          elevation: 0.0,
        ),
        body: Stack(
          children: [
            SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.max,
                children: [
                  Padding(
                    padding: const EdgeInsetsDirectional.fromSTEB(
                        16.0, 16.0, 16.0, 0.0),
                    child: Row(
                      mainAxisSize: MainAxisSize.max,
                      children: [
                        Text(
                          FFLocalizations.of(context).getText(
                            'hc95rt32' /* 레시피 목록 */,
                          ),
                          style: FlutterFlowTheme.of(context).titleLarge,
                        ),
                      ],
                    ),
                  ),
                  if (widget.recipeList != null &&
                      widget.recipeList!.isNotEmpty)
                    Padding(
                      padding: const EdgeInsetsDirectional.fromSTEB(
                          0.0, 12.0, 0.0, 0.0),
                      child: Builder(
                        builder: (context) {
                          final recipeList = widget.recipeList
                                  ?.map(
                                      (e) => RecipeModelStruct.maybeFromMap(e))
                                  .withoutNulls
                                  .toList()
                                  .toList() ??
                              [];
                          return ListView.builder(
                            padding: EdgeInsets.zero,
                            primary: false,
                            shrinkWrap: true,
                            scrollDirection: Axis.vertical,
                            itemCount: recipeList.length,
                            itemBuilder: (context, recipeListIndex) {
                              final recipeListItem =
                                  recipeList[recipeListIndex];
                              if (recipeListIndex == 0) {
                                return Showcase(
                                  key: _model.showcaseKeysList[0],
                                  title: FFLocalizations.of(context).getText(
                                    '1v3z1z9' /* 레시피 내용 확인하기 */,
                                  ),
                                  description:
                                      FFLocalizations.of(context).getText(
                                    '1d3z1z5' /* 상세한 레시피를 확인하려면 리스트의 레시피를 클릭하세요. */,
                                  ),
                                  targetBorderRadius: BorderRadius.circular(8),
                                  child: recipyListItemCell(
                                      context, recipeListItem, recipeListIndex),
                                );
                              } else {
                                return recipyListItemCell(
                                    context, recipeListItem, recipeListIndex);
                              }
                            },
                          );
                        },
                      ),
                    ),
                ],
              ),
            ),
            if (widget.userRecipeListView ?? true)
              Align(
                alignment: const AlignmentDirectional(1.0, 1.0),
                child: Padding(
                  padding: const EdgeInsets.all(15.0),
                  child: FlutterFlowIconButton(
                    borderRadius: 40.0,
                    borderWidth: 1.0,
                    buttonSize: 60.0,
                    fillColor: FlutterFlowTheme.of(context).tertiary,
                    icon: Icon(
                      Icons.add,
                      color: FlutterFlowTheme.of(context).primaryBackground,
                      size: 30.0,
                    ),
                    onPressed: () async {
                      // navToWriteNewRecipePageAction
                      context.pushNamed('WriteNewRecipePage');
                    },
                  ).animateOnPageLoad(
                      animationsMap['iconButtonOnPageLoadAnimation']!),
                ),
              ),
            if (!_model.isNetworkingOver)
              Align(
                alignment: const AlignmentDirectional(0.0, 0.0),
                child: Container(
                  width: double.infinity,
                  height: double.infinity,
                  decoration: const BoxDecoration(
                    color: Color(0x4B57636C),
                  ),
                ),
              ),
            if (!_model.isNetworkingOver)
              Align(
                alignment: const AlignmentDirectional(0.0, 0.0),
                child: Lottie.asset(
                  'assets/lottie_animations/Animation_-_1700555714208.json',
                  width: 150.0,
                  height: 130.0,
                  fit: BoxFit.cover,
                  animate: true,
                ),
              ),
            if (widget.recipeList == null)
              Align(
                alignment: const AlignmentDirectional(0.0, 0.0),
                child: Text(
                  FFLocalizations.of(context).getText(
                    'dg8ak59w' /* 레시피가 없습니다.
새로운 레시피를 만들어보세요. */
                    ,
                  ),
                  textAlign: TextAlign.center,
                  style: FlutterFlowTheme.of(context).bodyMedium,
                ),
              ),
          ],
        ),
      ),
    );
  }

  Padding recipyListItemCell(BuildContext context,
      RecipeModelStruct recipeListItem, int recipeListIndex) {
    return Padding(
      padding: const EdgeInsetsDirectional.fromSTEB(16.0, 0.0, 16.0, 8.0),
      child: Container(
        width: double.infinity,
        decoration: BoxDecoration(
          color: Colors.white,
          boxShadow: const [
            BoxShadow(
              blurRadius: 3.0,
              color: Color(0x411D2429),
              offset: Offset(0.0, 1.0),
            )
          ],
          borderRadius: BorderRadius.circular(8.0),
        ),
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: InkWell(
            splashColor: Colors.transparent,
            focusColor: Colors.transparent,
            hoverColor: Colors.transparent,
            highlightColor: Colors.transparent,
            onTap: () async {
              if (widget.userRecipeListView == true) {
                // getRecipeDetailUserList
                context.pushNamed(
                  'RecipeDetailPage',
                  queryParameters: {
                    'title': serializeParam(
                      recipeListItem.title,
                      ParamType.String,
                    ),
                    'time': serializeParam(
                      recipeListItem.time,
                      ParamType.String,
                    ),
                    'dscription': serializeParam(
                      recipeListItem.description,
                      ParamType.String,
                    ),
                    'ingredients': serializeParam(
                      recipeListItem.ingredients,
                      ParamType.String,
                      true,
                    ),
                    'steps': serializeParam(
                      recipeListItem.steps,
                      ParamType.String,
                      true,
                    ),
                    'index': serializeParam(
                      recipeListIndex,
                      ParamType.int,
                    ),
                    'isNewRecipe': serializeParam(
                      false,
                      ParamType.bool,
                    ),
                    'difficulty': serializeParam(
                      recipeListItem.difficulty,
                      ParamType.String,
                    ),
                  }.withoutNulls,
                );
              } else {
                // startLottieAnimationAction
                setState(() {
                  _model.isNetworkingOver = false;
                });
                if (!widget.isFirstTimeToStartApp!) {
                  if (_model.watchAd != true) {
                    // ShowInterstitialAction

                    _model.interstitialAdSuccess =
                        await admob.showInterstitialAd();

                    if (_model.interstitialAdSuccess!) {
                      // SetWatchAdTrueAction
                      setState(() {
                        _model.watchAd = true;
                      });
                    } else {
                      // ShowInterstitialErrorAction
                      await showDialog(
                        context: context,
                        builder: (alertDialogContext) {
                          return AlertDialog(
                            title: Text(
                                FFLocalizations.of(context).languageCode == 'en'
                                    ? 'Temporary malfunction'
                                    : '일시적인 장애'),
                            content: Text(FFLocalizations.of(context)
                                        .languageCode ==
                                    'en'
                                ? 'A temporary malfunction has occurred. Please check your communication environment, etc.'
                                : '일시적인 장애가 발생했습니다. 통신환경 등을 확인해주세요.'),
                            actions: [
                              TextButton(
                                onPressed: () =>
                                    Navigator.pop(alertDialogContext),
                                child: const Text('Ok'),
                              ),
                            ],
                          );
                        },
                      );
                    }
                  }
                } else {
                  // PassInterstitialAdAction
                  setState(() {
                    FFAppState().isFirstTimeToStartApp = false;
                    _model.watchAd = true;
                  });
                }
                // getRecipeDetailAPICall
                _model.getRecipeDetailResult = await CallOpenaiCall.call(
                  version: 'production',
                  title: valueOrDefault<String>(
                    recipeListItem.title,
                    'title',
                  ),
                  time: valueOrDefault<String>(
                    recipeListItem.time,
                    'time',
                  ),
                  difficulty: valueOrDefault<String>(
                    recipeListItem.difficulty,
                    'difficulty',
                  ),
                  purpose: 'getRecipeDetail',
                  ingredientsList: recipeListItem.ingredients,
                  language: FFLocalizations.of(context).languageCode,
                );
                if ((_model.getRecipeDetailResult?.succeeded ?? true)) {
                  // navigateToDetailPageWithApiResult

                  context.pushNamed(
                    'RecipeDetailPage',
                    queryParameters: {
                      'title': serializeParam(
                        recipeListItem.title,
                        ParamType.String,
                      ),
                      'time': serializeParam(
                        recipeListItem.time,
                        ParamType.String,
                      ),
                      'dscription': serializeParam(
                        getJsonField(
                          (_model.getRecipeDetailResult?.jsonBody ?? ''),
                          r'''$[0].description''',
                        ).toString(),
                        ParamType.String,
                      ),
                      'ingredients': serializeParam(
                        (getJsonField(
                          (_model.getRecipeDetailResult?.jsonBody ?? ''),
                          r'''$[0].ingredients''',
                          true,
                        ) as List)
                            .map<String>((s) => s.toString())
                            .toList(),
                        ParamType.String,
                        true,
                      ),
                      'steps': serializeParam(
                        (getJsonField(
                          (_model.getRecipeDetailResult?.jsonBody ?? ''),
                          r'''$[0].steps''',
                          true,
                        ) as List)
                            .map<String>((s) => s.toString())
                            .toList(),
                        ParamType.String,
                        true,
                      ),
                      'isNewRecipe': serializeParam(
                        true,
                        ParamType.bool,
                      ),
                      'difficulty': serializeParam(
                        recipeListItem.difficulty,
                        ParamType.String,
                      ),
                    }.withoutNulls,
                  );
                } else {
                  // networkErrorAction
                  await showDialog(
                    context: context,
                    builder: (alertDialogContext) {
                      return AlertDialog(
                        title: Text(
                            FFLocalizations.of(context).languageCode == 'en'
                                ? 'Error Message'
                                : '오류 메세지'),
                        content: Text(FFLocalizations.of(context)
                                    .languageCode ==
                                'en'
                            ? 'Retry is necessary due to a temporary network error.'
                            : '일시적 네트워크 오류로 인해 재시도가 필요합니다'),
                        actions: [
                          TextButton(
                            onPressed: () => Navigator.pop(alertDialogContext),
                            child: const Text('Ok'),
                          ),
                        ],
                      );
                    },
                  );
                }

                // stopLottieAnimationAction
                setState(() {
                  _model.isNetworkingOver = true;
                });
              }

              setState(() {});
            },
            child: Row(
              mainAxisSize: MainAxisSize.max,
              children: [
                Padding(
                  padding:
                      const EdgeInsetsDirectional.fromSTEB(0.0, 1.0, 1.0, 1.0),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(6.0),
                    child: Image.asset(
                      'assets/images/food.png',
                      width: 80.0,
                      height: 80.0,
                      fit: BoxFit.cover,
                    ),
                  ),
                ),
                Expanded(
                  child: Padding(
                    padding: const EdgeInsetsDirectional.fromSTEB(
                        8.0, 0.0, 8.0, 0.0),
                    child: Column(
                      mainAxisSize: MainAxisSize.max,
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          mainAxisSize: MainAxisSize.max,
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Flexible(
                              child: Text(
                                valueOrDefault<String>(
                                  recipeListItem.title,
                                  '리스트 타이틀',
                                ),
                                style: FlutterFlowTheme.of(context)
                                    .headlineSmall
                                    .override(
                                      fontFamily: 'Outfit',
                                      color: const Color(0xFF0F1113),
                                      fontSize: 20.0,
                                      fontWeight: FontWeight.normal,
                                    ),
                              ),
                            ),
                            Padding(
                              padding: const EdgeInsetsDirectional.fromSTEB(
                                  0.0, 4.0, 0.0, 0.0),
                              child: Container(
                                width: 40.0,
                                height: 40.0,
                                decoration: BoxDecoration(
                                  color: FlutterFlowTheme.of(context).tertiary,
                                  borderRadius: BorderRadius.circular(8.0),
                                ),
                                child: Icon(
                                  Icons.chevron_right_rounded,
                                  color: FlutterFlowTheme.of(context)
                                      .primaryBackground,
                                  size: 24.0,
                                ),
                              ),
                            ),
                          ],
                        ),
                        Row(
                          mainAxisSize: MainAxisSize.max,
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Row(
                              mainAxisSize: MainAxisSize.max,
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Padding(
                                  padding: const EdgeInsetsDirectional.fromSTEB(
                                      0.0, 0.0, 4.0, 0.0),
                                  child: Icon(
                                    Icons.access_time,
                                    color:
                                        FlutterFlowTheme.of(context).tertiary,
                                    size: 20.0,
                                  ),
                                ),
                                AutoSizeText(
                                  valueOrDefault<String>(
                                    recipeListItem.time,
                                    '10-20 분',
                                  ).maybeHandleOverflow(
                                    maxChars: 70,
                                    replacement: '…',
                                  ),
                                  textAlign: TextAlign.start,
                                  style: FlutterFlowTheme.of(context)
                                      .labelMedium
                                      .override(
                                        fontFamily: 'Plus Jakarta Sans',
                                        color: const Color(0xFF57636C),
                                        fontSize: 14.0,
                                        fontWeight: FontWeight.w500,
                                      ),
                                ),
                              ],
                            ),
                            Padding(
                              padding: const EdgeInsetsDirectional.fromSTEB(
                                  0.0, 4.0, 4.0, 0.0),
                              child: Row(
                                mainAxisSize: MainAxisSize.max,
                                children: [
                                  Padding(
                                    padding:
                                        const EdgeInsetsDirectional.fromSTEB(
                                            0.0, 0.0, 4.0, 0.0),
                                    child: Icon(
                                      Icons.star_half_rounded,
                                      color:
                                          FlutterFlowTheme.of(context).tertiary,
                                      size: 20.0,
                                    ),
                                  ),
                                  Text(
                                    formatNumber(
                                      functions.convertStringToDouble(
                                          valueOrDefault<String>(
                                        recipeListItem.difficulty,
                                        '3',
                                      )),
                                      formatType: FormatType.custom,
                                      format: '0',
                                      locale: '',
                                    ),
                                    textAlign: TextAlign.end,
                                    style: FlutterFlowTheme.of(context)
                                        .bodyMedium
                                        .override(
                                          fontFamily: 'Plus Jakarta Sans',
                                          color: const Color(0xFF0F1113),
                                          fontSize: 14.0,
                                          fontWeight: FontWeight.w500,
                                        ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
