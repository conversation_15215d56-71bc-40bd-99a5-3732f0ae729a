import '../database.dart';

class IngredientTable extends SupabaseTable<IngredientRow> {
  @override
  String get tableName => 'ingredient';

  @override
  IngredientRow createRow(Map<String, dynamic> data) => IngredientRow(data);
}

class IngredientRow extends SupabaseDataRow {
  IngredientRow(Map<String, dynamic> data) : super(data);

  @override
  SupabaseTable get table => IngredientTable();

  String get id => getField<String>('id')!;
  set id(String value) => setField<String>('id', value);

  DateTime get createdAt => getField<DateTime>('created_at')!;
  set createdAt(DateTime value) => setField<DateTime>('created_at', value);

  String? get name => getField<String>('name');
  set name(String? value) => setField<String>('name', value);
}
