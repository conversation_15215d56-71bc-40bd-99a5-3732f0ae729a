// ignore_for_file: unnecessary_getters_setters

import '/backend/schema/util/schema_util.dart';

import 'index.dart';
import '/flutter_flow/flutter_flow_util.dart';

class RecipeModelStruct extends BaseStruct {
  RecipeModelStruct({
    String? title,
    String? time,
    String? description,
    List<String>? steps,
    List<String>? ingredients,
    String? difficulty,
  })  : _title = title,
        _time = time,
        _description = description,
        _steps = steps,
        _ingredients = ingredients,
        _difficulty = difficulty;

  // "title" field.
  String? _title;
  String get title => _title ?? '';
  set title(String? val) => _title = val;
  bool hasTitle() => _title != null;

  // "time" field.
  String? _time;
  String get time => _time ?? '';
  set time(String? val) => _time = val;
  bool hasTime() => _time != null;

  // "description" field.
  String? _description;
  String get description => _description ?? '';
  set description(String? val) => _description = val;
  bool hasDescription() => _description != null;

  // "steps" field.
  List<String>? _steps;
  List<String> get steps => _steps ?? const [];
  set steps(List<String>? val) => _steps = val;
  void updateSteps(Function(List<String>) updateFn) => updateFn(_steps ??= []);
  bool hasSteps() => _steps != null;

  // "ingredients" field.
  List<String>? _ingredients;
  List<String> get ingredients => _ingredients ?? const [];
  set ingredients(List<String>? val) => _ingredients = val;
  void updateIngredients(Function(List<String>) updateFn) =>
      updateFn(_ingredients ??= []);
  bool hasIngredients() => _ingredients != null;

  // "difficulty" field.
  String? _difficulty;
  String get difficulty => _difficulty ?? '';
  set difficulty(String? val) => _difficulty = val;
  bool hasDifficulty() => _difficulty != null;

  static RecipeModelStruct fromMap(Map<String, dynamic> data) =>
      RecipeModelStruct(
        title: data['title'] as String?,
        time: data['time'] as String?,
        description: data['description'] as String?,
        steps: getDataList(data['steps']),
        ingredients: getDataList(data['ingredients']),
        difficulty: data['difficulty'] as String?,
      );

  static RecipeModelStruct? maybeFromMap(dynamic data) => data is Map
      ? RecipeModelStruct.fromMap(data.cast<String, dynamic>())
      : null;

  Map<String, dynamic> toMap() => {
        'title': _title,
        'time': _time,
        'description': _description,
        'steps': _steps,
        'ingredients': _ingredients,
        'difficulty': _difficulty,
      }.withoutNulls;

  @override
  Map<String, dynamic> toSerializableMap() => {
        'title': serializeParam(
          _title,
          ParamType.String,
        ),
        'time': serializeParam(
          _time,
          ParamType.String,
        ),
        'description': serializeParam(
          _description,
          ParamType.String,
        ),
        'steps': serializeParam(
          _steps,
          ParamType.String,
          true,
        ),
        'ingredients': serializeParam(
          _ingredients,
          ParamType.String,
          true,
        ),
        'difficulty': serializeParam(
          _difficulty,
          ParamType.String,
        ),
      }.withoutNulls;

  static RecipeModelStruct fromSerializableMap(Map<String, dynamic> data) =>
      RecipeModelStruct(
        title: deserializeParam(
          data['title'],
          ParamType.String,
          false,
        ),
        time: deserializeParam(
          data['time'],
          ParamType.String,
          false,
        ),
        description: deserializeParam(
          data['description'],
          ParamType.String,
          false,
        ),
        steps: deserializeParam<String>(
          data['steps'],
          ParamType.String,
          true,
        ),
        ingredients: deserializeParam<String>(
          data['ingredients'],
          ParamType.String,
          true,
        ),
        difficulty: deserializeParam(
          data['difficulty'],
          ParamType.String,
          false,
        ),
      );

  @override
  String toString() => 'RecipeModelStruct(${toMap()})';

  @override
  bool operator ==(Object other) {
    const listEquality = ListEquality();
    return other is RecipeModelStruct &&
        title == other.title &&
        time == other.time &&
        description == other.description &&
        listEquality.equals(steps, other.steps) &&
        listEquality.equals(ingredients, other.ingredients) &&
        difficulty == other.difficulty;
  }

  @override
  int get hashCode => const ListEquality()
      .hash([title, time, description, steps, ingredients, difficulty]);
}

RecipeModelStruct createRecipeModelStruct({
  String? title,
  String? time,
  String? description,
  String? difficulty,
}) =>
    RecipeModelStruct(
      title: title,
      time: time,
      description: description,
      difficulty: difficulty,
    );
