import 'dart:convert';
import 'dart:typed_data';
import '../schema/structs/index.dart';

import '/flutter_flow/flutter_flow_util.dart';
import 'api_manager.dart';

export 'api_manager.dart' show ApiCallResponse;

const _kPrivateApiFunctionName = 'ffPrivateApiCall';

class CallOpenaiCall {
  static Future<ApiCallResponse> call({
    List<String>? ingredientsList,
    String? time = '0',
    String? difficulty = '0',
    String? version = 'test',
    String? purpose = 'getRecipeList',
    String? title = 'title',
    String? language = 'Korean',
  }) async {
    final ingredients = _serializeList(ingredientsList);

    final ffApiRequestBody = '''
{
  "version": "${version}",
  "purpose": "${purpose}",
  "language": "${language}",
  "data": {
    "ingredients": ${ingredients},
    "title": "${title}",
    "time": "${time}",
    "difficulty": "${difficulty}"
  }
}''';
    return ApiManager.instance.makeApiCall(
      callName: 'callOpenai',
      apiUrl: 'https://rzttfspatzltdsdhpzzp.supabase.co/functions/v1/openai',
      callType: ApiCallType.POST,
      headers: {
        'Content-Type': 'application/json',
      },
      params: {},
      body: ffApiRequestBody,
      bodyType: BodyType.JSON,
      returnBody: true,
      encodeBodyUtf8: true,
      decodeUtf8: true,
      cache: false,
      alwaysAllowBody: false,
    );
  }

  static List? res(dynamic response) => getJsonField(
        response,
        r'''$''',
        true,
      ) as List?;
  static List? foodNames(dynamic response) => getJsonField(
        response,
        r'''$.title''',
        true,
      ) as List?;
}

class CallfeatureflagsCall {
  static Future<ApiCallResponse> call() async {
    return ApiManager.instance.makeApiCall(
      callName: 'callfeatureflags',
      apiUrl:
          'https://rzttfspatzltdsdhpzzp.supabase.co/functions/v1/feature_flags',
      callType: ApiCallType.POST,
      headers: {},
      params: {},
      bodyType: BodyType.JSON,
      returnBody: true,
      encodeBodyUtf8: false,
      decodeUtf8: false,
      cache: false,
      alwaysAllowBody: false,
    );
  }

  static bool? englishEnabled(dynamic response) =>
      castToType<bool>(getJsonField(
        response,
        r'''$.englishEnabled''',
      ));
}

class ApiPagingParams {
  int nextPageNumber = 0;
  int numItems = 0;
  dynamic lastResponse;

  ApiPagingParams({
    required this.nextPageNumber,
    required this.numItems,
    required this.lastResponse,
  });

  @override
  String toString() =>
      'PagingParams(nextPageNumber: $nextPageNumber, numItems: $numItems, lastResponse: $lastResponse,)';
}

String _serializeList(List? list) {
  list ??= <String>[];
  try {
    return json.encode(list);
  } catch (_) {
    return '[]';
  }
}

String _serializeJson(dynamic jsonVar, [bool isList = false]) {
  jsonVar ??= (isList ? [] : {});
  try {
    return json.encode(jsonVar);
  } catch (_) {
    return isList ? '[]' : '{}';
  }
}
