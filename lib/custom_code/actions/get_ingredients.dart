// Automatic FlutterFlow imports
import '/backend/schema/structs/index.dart';
import '/backend/supabase/supabase.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import 'index.dart'; // Imports other custom actions
import '/flutter_flow/custom_functions.dart'; // Imports custom functions
import 'package:flutter/material.dart';
// Begin custom action code
// DO NOT REMOVE OR MODIFY THE CODE ABOVE!

Future<List<String>> getIngredients(String language) async {
  final supabase = SupaFlow.client;

  final response = await supabase
      .from('ingredient')
      .select('name')
      .eq('language', language)
      .order('name')
      .order('frequency', ascending: false);

  // check if the query was successful and if there were rows returned.
  if (response.isEmpty) {
    return [];
  }

  // Extract the names of ingredients from the response data.
  List<String> ingredientNames = [];
  for (var row in response) {
    ingredientNames.add(row['name'].toString());
  }

  ingredientNames.sort((a, b) {
    // First, compare by the length of strings
    int lengthComparison = a.length.compareTo(b.length);

    // If the lengths are the same, compare alphabetically
    if (lengthComparison == 0) {
      return a.compareTo(b);
    } else {
      return lengthComparison; // Sort by length
    }
  });

  return ingredientNames;
}
