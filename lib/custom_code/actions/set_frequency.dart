import '/backend/supabase/supabase.dart';

void setFrequency(String name, String language) async {
  final supabase = SupaFlow.client;

  final oldNumber = await supabase
      .from('ingredient')
      .select('frequency')
      .eq('name', name)
      .eq('language', language)
      .single();

  await supabase
      .from('ingredient')
      .update({
        'frequency': oldNumber['frequency'] + 1,
      })
      .eq('name', name)
      .eq('language', language);
}
