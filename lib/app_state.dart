import 'package:flutter/material.dart';
import '/backend/schema/structs/index.dart';
import 'backend/api_requests/api_manager.dart';
import 'backend/supabase/supabase.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'flutter_flow/flutter_flow_util.dart';

class FFAppState extends ChangeNotifier {
  static FFAppState _instance = FFAppState._internal();

  factory FFAppState() {
    return _instance;
  }

  FFAppState._internal();

  static void reset() {
    _instance = FFAppState._internal();
  }

  Future initializePersistedState() async {
    prefs = await SharedPreferences.getInstance();
    _safeInit(() {
      _UserRecipeList = prefs
              .getStringList('ff_UserRecipeList')
              ?.map((x) {
                try {
                  return RecipeModelStruct.fromSerializableMap(jsonDecode(x));
                } catch (e) {
                  print("Can't decode persisted data type. Error: $e.");
                  return null;
                }
              })
              .withoutNulls
              .toList() ??
          _UserRecipeList;
    });
    _safeInit(() {
      _timeOptionList =
          prefs.getStringList('ff_timeOptionList') ?? _timeOptionList;
    });
    _safeInit(() {
      _timeOptionListEn =
          prefs.getStringList('ff_timeOptionListEn') ?? _timeOptionListEn;
    });
    _safeInit(() {
      _isFirstTimeToStartApp =
          prefs.getBool('ff_isFirstTimeToStartApp') ?? _isFirstTimeToStartApp;
    });
    _safeInit(() {
      _mainPageShowCase =
          prefs.getBool('ff_mainPageShowCase') ?? _mainPageShowCase;
    });
    _safeInit(() {
      _recipeListPageShowCase =
          prefs.getBool('ff_recipeListPageShowCase') ?? _recipeListPageShowCase;
    });
  }

  void update(VoidCallback callback) {
    callback();
    notifyListeners();
  }

  late SharedPreferences prefs;

  List<String> _ingredients = [];
  List<String> get ingredients => _ingredients;
  set ingredients(List<String> _value) {
    _ingredients = _value;
  }

  void addToIngredients(String _value) {
    _ingredients.add(_value);
  }

  void removeFromIngredients(String _value) {
    _ingredients.remove(_value);
  }

  void removeAtIndexFromIngredients(int _index) {
    _ingredients.removeAt(_index);
  }

  void updateIngredientsAtIndex(
    int _index,
    String Function(String) updateFn,
  ) {
    _ingredients[_index] = updateFn(_ingredients[_index]);
  }

  void insertAtIndexInIngredients(int _index, String _value) {
    _ingredients.insert(_index, _value);
  }

  bool _isSearched = true;
  bool get isSearched => _isSearched;
  set isSearched(bool _value) {
    _isSearched = _value;
  }

  List<RecipeModelStruct> _UserRecipeList = [];
  List<RecipeModelStruct> get UserRecipeList => _UserRecipeList;
  set UserRecipeList(List<RecipeModelStruct> _value) {
    _UserRecipeList = _value;
    prefs.setStringList(
        'ff_UserRecipeList', _value.map((x) => x.serialize()).toList());
  }

  void addToUserRecipeList(RecipeModelStruct _value) {
    _UserRecipeList.add(_value);
    prefs.setStringList('ff_UserRecipeList',
        _UserRecipeList.map((x) => x.serialize()).toList());
  }

  void removeFromUserRecipeList(RecipeModelStruct _value) {
    _UserRecipeList.remove(_value);
    prefs.setStringList('ff_UserRecipeList',
        _UserRecipeList.map((x) => x.serialize()).toList());
  }

  void removeAtIndexFromUserRecipeList(int _index) {
    _UserRecipeList.removeAt(_index);
    prefs.setStringList('ff_UserRecipeList',
        _UserRecipeList.map((x) => x.serialize()).toList());
  }

  void updateUserRecipeListAtIndex(
    int _index,
    RecipeModelStruct Function(RecipeModelStruct) updateFn,
  ) {
    _UserRecipeList[_index] = updateFn(_UserRecipeList[_index]);
    prefs.setStringList('ff_UserRecipeList',
        _UserRecipeList.map((x) => x.serialize()).toList());
  }

  void insertAtIndexInUserRecipeList(int _index, RecipeModelStruct _value) {
    _UserRecipeList.insert(_index, _value);
    prefs.setStringList('ff_UserRecipeList',
        _UserRecipeList.map((x) => x.serialize()).toList());
  }

  List<String> _timeOptionList = ['10-20 분', '20-30 분', '30-45 분', '45 분 이상'];
  List<String> get timeOptionList => _timeOptionList;
  set timeOptionList(List<String> _value) {
    _timeOptionList = _value;
    prefs.setStringList('ff_timeOptionList', _value);
  }

  void addToTimeOptionList(String _value) {
    _timeOptionList.add(_value);
    prefs.setStringList('ff_timeOptionList', _timeOptionList);
  }

  void removeFromTimeOptionList(String _value) {
    _timeOptionList.remove(_value);
    prefs.setStringList('ff_timeOptionList', _timeOptionList);
  }

  void removeAtIndexFromTimeOptionList(int _index) {
    _timeOptionList.removeAt(_index);
    prefs.setStringList('ff_timeOptionList', _timeOptionList);
  }

  void updateTimeOptionListAtIndex(
    int _index,
    String Function(String) updateFn,
  ) {
    _timeOptionList[_index] = updateFn(_timeOptionList[_index]);
    prefs.setStringList('ff_timeOptionList', _timeOptionList);
  }

  void insertAtIndexInTimeOptionList(int _index, String _value) {
    _timeOptionList.insert(_index, _value);
    prefs.setStringList('ff_timeOptionList', _timeOptionList);
  }

  List<String> _timeOptionListEn = [];
  List<String> get timeOptionListEn => _timeOptionListEn;
  set timeOptionListEn(List<String> _value) {
    _timeOptionListEn = _value;
    prefs.setStringList('ff_timeOptionListEn', _value);
  }

  void addToTimeOptionListEn(String _value) {
    _timeOptionListEn.add(_value);
    prefs.setStringList('ff_timeOptionListEn', _timeOptionListEn);
  }

  void removeFromTimeOptionListEn(String _value) {
    _timeOptionListEn.remove(_value);
    prefs.setStringList('ff_timeOptionListEn', _timeOptionListEn);
  }

  void removeAtIndexFromTimeOptionListEn(int _index) {
    _timeOptionListEn.removeAt(_index);
    prefs.setStringList('ff_timeOptionListEn', _timeOptionListEn);
  }

  void updateTimeOptionListEnAtIndex(
    int _index,
    String Function(String) updateFn,
  ) {
    _timeOptionListEn[_index] = updateFn(_timeOptionListEn[_index]);
    prefs.setStringList('ff_timeOptionListEn', _timeOptionListEn);
  }

  void insertAtIndexInTimeOptionListEn(int _index, String _value) {
    _timeOptionListEn.insert(_index, _value);
    prefs.setStringList('ff_timeOptionListEn', _timeOptionListEn);
  }

  bool _isFirstTimeToStartApp = true;
  bool get isFirstTimeToStartApp => _isFirstTimeToStartApp;
  set isFirstTimeToStartApp(bool _value) {
    _isFirstTimeToStartApp = _value;
    prefs.setBool('ff_isFirstTimeToStartApp', _value);
  }

  bool _mainPageShowCase = true;
  bool get mainPageShowCase => _mainPageShowCase;
  set mainPageShowCase(bool _value) {
    _mainPageShowCase = _value;
    prefs.setBool('ff_mainPageShowCase', _value);
  }

  bool _recipeListPageShowCase = true;
  bool get recipeListPageShowCase => _recipeListPageShowCase;
  set recipeListPageShowCase(bool _value) {
    _recipeListPageShowCase = _value;
    prefs.setBool('ff_recipeListPageShowCase', _value);
  }
}

LatLng? _latLngFromString(String? val) {
  if (val == null) {
    return null;
  }
  final split = val.split(',');
  final lat = double.parse(split.first);
  final lng = double.parse(split.last);
  return LatLng(lat, lng);
}

void _safeInit(Function() initializeField) {
  try {
    initializeField();
  } catch (_) {}
}

Future _safeInitAsync(Function() initializeField) async {
  try {
    await initializeField();
  } catch (_) {}
}
