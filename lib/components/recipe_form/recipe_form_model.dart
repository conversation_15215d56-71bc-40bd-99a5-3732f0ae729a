import '/backend/supabase/supabase.dart';
import '/components/rating_text_component/rating_text_component_widget.dart';
import '/flutter_flow/flutter_flow_animations.dart';
import '/flutter_flow/flutter_flow_autocomplete_options_list.dart';
import '/flutter_flow/flutter_flow_icon_button.dart';
import '/flutter_flow/flutter_flow_radio_button.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/flutter_flow/flutter_flow_widgets.dart';
import '/flutter_flow/form_field_controller.dart';
import '/flutter_flow/custom_functions.dart' as functions;
import 'recipe_form_widget.dart' show RecipeFormWidget;
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter/services.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';

class RecipeFormModel extends FlutterFlowModel<RecipeFormWidget> {
  ///  Local state fields for this component.

  bool isIngredientsSearchButtonTapped = false;

  bool isTimesCardTapped = true;

  bool isDifficultyCardTapped = true;

  bool isStepsCardTapped = true;

  List<String> steps = [];
  void addToSteps(String item) => steps.add(item);
  void removeFromSteps(String item) => steps.remove(item);
  void removeAtIndexFromSteps(int index) => steps.removeAt(index);
  void insertAtIndexInSteps(int index, String item) =>
      steps.insert(index, item);
  void updateStepsAtIndex(int index, Function(String) updateFn) =>
      steps[index] = updateFn(steps[index]);

  List<String> ingredients = [];
  void addToIngredients(String item) => ingredients.add(item);
  void removeFromIngredients(String item) => ingredients.remove(item);
  void removeAtIndexFromIngredients(int index) => ingredients.removeAt(index);
  void insertAtIndexInIngredients(int index, String item) =>
      ingredients.insert(index, item);
  void updateIngredientsAtIndex(int index, Function(String) updateFn) =>
      ingredients[index] = updateFn(ingredients[index]);

  String time = '';

  double? rating;

  ///  State fields for stateful widgets in this component.

  // State field(s) for TitleTextField widget.
  FocusNode? titleTextFieldFocusNode;
  TextEditingController? titleTextFieldController;
  String? Function(BuildContext, String?)? titleTextFieldControllerValidator;
  // State field(s) for DescriptionTextField widget.
  FocusNode? descriptionTextFieldFocusNode;
  TextEditingController? descriptionTextFieldController;
  String? Function(BuildContext, String?)?
      descriptionTextFieldControllerValidator;
  // State field(s) for SearchTextField widget.
  final searchTextFieldKey = GlobalKey();
  FocusNode? searchTextFieldFocusNode;
  TextEditingController? searchTextFieldController;
  String? searchTextFieldSelectedOption;
  String? Function(BuildContext, String?)? searchTextFieldControllerValidator;
  // State field(s) for TimesRadioButton widget.
  FormFieldController<String>? timesRadioButtonValueController;
  // Model for RatingTextComponent component.
  late RatingTextComponentModel ratingTextComponentModel;
  // State field(s) for RatingBar widget.
  double? ratingBarValue;
  // State field(s) for StepsTextField widget.
  FocusNode? stepsTextFieldFocusNode;
  TextEditingController? stepsTextFieldController;
  String? Function(BuildContext, String?)? stepsTextFieldControllerValidator;

  /// Initialization and disposal methods.

  void initState(BuildContext context) {
    ratingTextComponentModel =
        createModel(context, () => RatingTextComponentModel());
  }

  void dispose() {
    titleTextFieldFocusNode?.dispose();
    titleTextFieldController?.dispose();

    descriptionTextFieldFocusNode?.dispose();
    descriptionTextFieldController?.dispose();

    searchTextFieldFocusNode?.dispose();

    ratingTextComponentModel.dispose();
    stepsTextFieldFocusNode?.dispose();
    stepsTextFieldController?.dispose();
  }

  /// Action blocks are added here.

  /// Additional helper methods are added here.

  String? get timesRadioButtonValue => timesRadioButtonValueController?.value;
}
