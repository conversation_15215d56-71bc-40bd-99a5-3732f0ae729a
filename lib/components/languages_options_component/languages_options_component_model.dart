import '/flutter_flow/flutter_flow_icon_button.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/custom_code/actions/index.dart' as actions;
import 'languages_options_component_widget.dart'
    show LanguagesOptionsComponentWidget;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';

class LanguagesOptionsComponentModel
    extends FlutterFlowModel<LanguagesOptionsComponentWidget> {
  ///  State fields for stateful widgets in this component.

  // Stores action output result for [Custom Action - getIngredients] action in ItemRow widget.
  List<String>? supabaseIngredientList;
  // Stores action output result for [Custom Action - getIngredients] action in ItemRow widget.
  List<String>? supabaseIngredientListEn;

  /// Initialization and disposal methods.

  void initState(BuildContext context) {}

  void dispose() {}

  /// Action blocks are added here.

  /// Additional helper methods are added here.
}
