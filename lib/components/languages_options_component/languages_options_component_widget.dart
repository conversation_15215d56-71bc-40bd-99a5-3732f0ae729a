import 'package:my_chef/flutter_flow/flutter_flow_widgets.dart';

import '/flutter_flow/flutter_flow_icon_button.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/custom_code/actions/index.dart' as actions;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import 'languages_options_component_model.dart';
export 'languages_options_component_model.dart';

class LanguagesOptionsComponentWidget extends StatefulWidget {
  const LanguagesOptionsComponentWidget({super.key});

  @override
  _LanguagesOptionsComponentWidgetState createState() =>
      _LanguagesOptionsComponentWidgetState();
}

class _LanguagesOptionsComponentWidgetState
    extends State<LanguagesOptionsComponentWidget> {
  late LanguagesOptionsComponentModel _model;

  @override
  void setState(VoidCallback callback) {
    super.setState(callback);
    _model.onUpdate();
  }

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => LanguagesOptionsComponentModel());
  }

  @override
  void dispose() {
    _model.maybeDispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    context.watch<FFAppState>();

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Container(
        width: MediaQuery.sizeOf(context).width * 0.9,
        decoration: BoxDecoration(
          color: FlutterFlowTheme.of(context).secondaryBackground,
          boxShadow: const [
            BoxShadow(
              blurRadius: 4.0,
              color: Color(0x33000000),
              offset: Offset(0.0, 2.0),
            )
          ],
          borderRadius: BorderRadius.circular(12.0),
        ),
        child: Padding(
          padding: const EdgeInsetsDirectional.fromSTEB(0.0, 0.0, 0.0, 15.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: const EdgeInsetsDirectional.fromSTEB(
                    12.0, 12.0, 12.0, 12.0),
                child: Row(
                  mainAxisSize: MainAxisSize.max,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      FFLocalizations.of(context).getText(
                        '2a5ucwxh' /* 옵션 */,
                      ),
                      textAlign: TextAlign.start,
                      style: FlutterFlowTheme.of(context).labelMedium,
                    ),
                    FlutterFlowIconButton(
                      borderRadius: 20.0,
                      borderWidth: 1.0,
                      buttonSize: 40.0,
                      icon: Icon(
                        Icons.close_rounded,
                        color: FlutterFlowTheme.of(context).tertiary,
                        size: 24.0,
                      ),
                      onPressed: () async {
                        // DismissBottomSheetAction
                        Navigator.pop(context);
                      },
                    ),
                  ],
                ),
              ),
              Container(
                width: double.infinity,
                decoration: BoxDecoration(
                  color: FlutterFlowTheme.of(context).secondaryBackground,
                ),
                child: Padding(
                    padding: const EdgeInsetsDirectional.fromSTEB(
                        10.0, 8.0, 10.0, 4.0),
                    child: FFButtonWidget(
                      onPressed: () async {
                        // SetKoreanAction
                        setAppLanguage(context, 'ko');
                        // GetIngredientsFormSupabaseAction
                        _model.supabaseIngredientList =
                            await actions.getIngredients(
                          'ko',
                        );
                        // UpdateAppIngredientsAction
                        setState(() {
                          FFAppState().ingredients = _model
                              .supabaseIngredientList!
                              .toList()
                              .cast<String>();
                        });
                        // DismissAction
                        Navigator.pop(context);

                        setState(() {});
                      },
                      text: FFLocalizations.of(context).getText(
                        'pga8sbl8' /* 🇰🇷 한국어 */,
                      ),
                      options: FFButtonOptions(
                        width: double.infinity,
                        height: 40.0,
                        color: Colors.white,
                        textStyle: FlutterFlowTheme.of(context).bodyMedium,
                      ),
                    )),
              ),
              Container(
                width: double.infinity,
                decoration: BoxDecoration(
                  color: FlutterFlowTheme.of(context).secondaryBackground,
                ),
                child: Align(
                  alignment: const AlignmentDirectional(0.0, 0.0),
                  child: SizedBox(
                    width: double.infinity,
                    child: Stack(
                      children: [
                        Padding(
                            padding: const EdgeInsetsDirectional.fromSTEB(
                                10.0, 4.0, 10.0, 8.0),
                            child: FFButtonWidget(
                              onPressed: () async {
                                // SetEnglishAction
                                setAppLanguage(context, 'en');
                                await FFLocalizations.storeLocale('en');
                                // GetIngredientsFormSupabaseAction
                                _model.supabaseIngredientListEn =
                                    await actions.getIngredients(
                                  'en',
                                );
                                // UpdateAppIngredientsAction
                                setState(() {
                                  FFAppState().ingredients = _model
                                      .supabaseIngredientListEn!
                                      .toList()
                                      .cast<String>();
                                });
                                // DismissAction
                                Navigator.pop(context);

                                setState(() {});
                              },
                              text: FFLocalizations.of(context).getText(
                                'svkzpxc0' /* 🇺🇸 English */,
                              ),
                              options: FFButtonOptions(
                                width: double.infinity,
                                height: 40.0,
                                color: Colors.white,
                                textStyle:
                                    FlutterFlowTheme.of(context).bodyMedium,
                              ),
                            )),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
