import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import 'rating_text_component_model.dart';
export 'rating_text_component_model.dart';

class RatingTextComponentWidget extends StatefulWidget {
  const RatingTextComponentWidget({
    Key? key,
    required this.difficulty,
    required this.fontSize,
  }) : super(key: key);

  final double? difficulty;
  final double? fontSize;

  @override
  _RatingTextComponentWidgetState createState() =>
      _RatingTextComponentWidgetState();
}

class _RatingTextComponentWidgetState extends State<RatingTextComponentWidget> {
  late RatingTextComponentModel _model;

  @override
  void setState(VoidCallback callback) {
    super.setState(callback);
    _model.onUpdate();
  }

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => RatingTextComponentModel());
  }

  @override
  void dispose() {
    _model.maybeDispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    context.watch<FFAppState>();

    return Text(
      formatNumber(
        widget.difficulty,
        formatType: FormatType.custom,
        format: '0',
        locale: '',
      ),
      style: FlutterFlowTheme.of(context).bodyLarge.override(
            fontFamily: 'Readex Pro',
            fontSize: valueOrDefault<double>(
              widget.fontSize,
              14.0,
            ),
            fontWeight: FontWeight.w600,
          ),
    );
  }
}
